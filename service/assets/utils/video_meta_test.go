package utils

import (
	"gofly/app/client/entity"
	"testing"
)

func TestPreParseAssetVideoMetaInfoWithURL(t *testing.T) {
	// 测试用例
	tests := []struct {
		name        string
		douyinURL   string
		expectError bool
	}{
		{
			name:        "valid douyin video URL",
			douyinURL:   "https://www.douyin.com/video/7234567890123456789",
			expectError: false, // 注意：这个测试可能失败，因为需要真实的数据库和MediaCrawler服务
		},
		{
			name:        "douyin short URL",
			douyinURL:   "这是一个分享链接：https://v.douyin.com/iFRvEaH/ 快来看看！",
			expectError: false, // 注意：这个测试可能失败，因为需要真实的网络请求
		},
		{
			name:        "invalid URL",
			douyinURL:   "这是一段没有抖音链接的文本",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			asset := &entity.Assets{
				DouyinURL: tt.douyinURL,
			}

			err := PreParseAssetVideoMetaInfo(asset)

			if tt.expectError {
				if err == nil {
					t.Errorf("PreParseAssetVideoMetaInfo() expected error for input: %v", tt.douyinURL)
				}
			} else {
				if err != nil {
					// 对于需要网络请求和数据库的测试，记录日志但不视为失败
					t.Logf("PreParseAssetVideoMetaInfo() error = %v (可能是网络或数据库问题)", err)
				} else {
					t.Logf("PreParseAssetVideoMetaInfo() success, VideoID=%s, Title=%s", asset.VideoID, asset.VideoTitle)
				}
			}
		})
	}
}
