package utils

import (
	"fmt"
	"gofly/app/client/entity"
	assets_mapper "gofly/app/client/mappers/assets"
	"gofly/service/douyin"

	"dario.cat/mergo"
)

// PreParseAssetVideoMetaInfo 预解析视频元数据信息
// 从抖音URL中提取视频元数据并填充到资产对象中
func PreParseAssetVideoMetaInfo(asset *entity.Assets) error {
	douyinAweme, fullURL, err := douyin.GetVideoMetaInfo(asset.DouyinURL)
	if err != nil {
		return err
	}

	// 使用mapper创建新的数据，然后合并到现有asset中
	newAssetData := assets_mapper.FromDouyinAweme(douyinAweme, fullURL)

	// 使用mergo合并新旧数据，新数据会覆盖旧数据中的对应字段
	if err := mergo.Merge(asset, newAssetData, mergo.WithOverride); err != nil {
		return fmt.Errorf("合并asset数据失败: %w", err)
	}

	return nil
}
