# Assets Service Utils

## 概述

`service/assets/utils` 包含 Assets 服务层的工具函数，主要用于处理资产相关的业务逻辑。

## 文件结构

```
service/assets/utils/
├── README.md              # 本文档
├── video_meta.go          # 视频元数据处理工具
└── video_meta_test.go     # 视频元数据处理测试
```

## 功能模块

### 视频元数据处理 (`video_meta.go`)

#### PreParseAssetVideoMetaInfo

**功能**: 预解析视频元数据信息，从抖音URL中提取视频元数据并填充到资产对象中。

**签名**:
```go
func PreParseAssetVideoMetaInfo(asset *entity.Assets) error
```

**参数**:
- `asset`: 资产对象指针，包含抖音URL

**返回值**:
- `error`: 处理过程中的错误，成功时返回 nil

**功能描述**:
1. 调用 `douyin.GetVideoMetaInfo` 从抖音URL获取视频信息
2. 使用 `assets_mapper.FromDouyinAweme` 创建新的数据结构
3. 使用 `mergo.Merge` 合并新旧数据到现有的 asset 对象中
4. 保持数据不可变性的设计模式

**使用示例**:
```go
import "gofly/service/assets/utils"

asset := &entity.Assets{
    DouyinURL: "https://www.douyin.com/video/7234567890123456789",
}

err := utils.PreParseAssetVideoMetaInfo(asset)
if err != nil {
    log.Printf("预解析失败: %v", err)
    return
}

// asset 对象现在包含了从抖音获取的元数据
fmt.Printf("视频标题: %s\n", asset.VideoTitle)
fmt.Printf("作者: %s\n", asset.Author)
```

## 依赖关系

### 内部依赖
- `gofly/app/client/entity` - 实体定义
- `gofly/app/client/mappers/assets` - 数据映射器
- `gofly/service/douyin` - 抖音服务

### 外部依赖
- `dario.cat/mergo` - 结构体合并库

## 设计原则

### 1. 分层架构
- 位于 service 层，处理业务逻辑
- 不直接处理 HTTP 请求或响应
- 专注于数据处理和转换

### 2. 数据不可变性
- 使用 mapper 创建新的数据结构
- 通过 mergo 合并数据，避免直接修改
- 保持函数的副作用最小化

### 3. 错误处理
- 提供详细的错误信息
- 使用 `fmt.Errorf` 包装错误
- 保持错误链的完整性

## 测试

运行测试:
```bash
go test ./service/assets/utils/ -v
```

测试覆盖了多种场景:
- 有效的抖音视频URL
- 抖音短链接
- 无效的URL输入

## 迁移说明

该包中的 `PreParseAssetVideoMetaInfo` 函数原本位于 `app/client/assets/utils/assets.go`，为了更好的架构分层而迁移到此处。

### 迁移原因
1. **架构分层**: 该函数属于业务逻辑，应该位于 service 层
2. **职责清晰**: service 层更适合处理复杂的数据转换和外部服务调用
3. **依赖管理**: 避免 client 层过度依赖外部服务

### 调用方式更新
```go
// 原来的调用
import assets_utils "gofly/app/client/assets/utils"
assets_utils.PreParseAssetVideoMetaInfo(asset)

// 新的调用
import "gofly/service/assets/utils"
utils.PreParseAssetVideoMetaInfo(asset)
```

## 最佳实践

1. **错误处理**: 始终检查返回的错误
2. **数据验证**: 确保传入的 asset 对象包含有效的 DouyinURL
3. **并发安全**: 函数是并发安全的，可以在多个 goroutine 中使用
4. **性能考虑**: 函数涉及网络请求，建议添加适当的超时控制
