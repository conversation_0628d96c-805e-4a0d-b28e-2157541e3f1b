package douyin

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestProcessDouyinURLFromText_Integration(t *testing.T) {
	// 跳过集成测试，除非设置了集成测试标志
	if testing.Short() {
		t.<PERSON><PERSON>("skipping integration test")
	}

	processor := NewURLProcessor()

	tests := []struct {
		name        string
		text        string
		expectError bool
	}{
		{
			name:        "text with douyin URL",
			text:        "https://www.douyin.com/channel/300203?modal_id=6948258823744343326",
			expectError: false,
		},
		{
			name:        "text without URLs",
			text:        "这是一段没有链接的文本",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fullURL, awemeID, err := processor.ProcessDouyinURLFromText(tt.text)
			if tt.expectError {
				if err == nil {
					t.<PERSON><PERSON><PERSON>("ProcessDouyinURLFromText() expected error, but got result: fullURL=%v, awemeID=%v", fullURL, awemeID)
				}
			} else {
				if err != nil {
					t.Errorf("ProcessDouyinURLFromText() error = %v", err)
				}
				if fullURL == "" {
					t.Errorf("ProcessDouyinURLFromText() returned empty fullURL")
				}
				// awemeID 可能为空，因为不是所有转换后的URL都包含video路径
				t.Logf("ProcessDouyinURLFromText() fullURL=%v, awemeID=%v", fullURL, awemeID)
			}
		})
	}
}

// 单元测试：测试文本处理逻辑（不涉及网络请求）
func TestProcessDouyinURLFromText_Unit(t *testing.T) {
	processor := NewURLProcessor()

	tests := []struct {
		name        string
		text        string
		expectError bool
	}{
		{
			name:        "text without URLs",
			text:        "这是一段没有链接的文本",
			expectError: true,
		},
		{
			name:        "empty text",
			text:        "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, _, err := processor.ProcessDouyinURLFromText(tt.text)
			if tt.expectError {
				if err == nil {
					t.Errorf("ProcessDouyinURLFromText() expected error for input: %v", tt.text)
				}
			} else {
				if err != nil {
					t.Errorf("ProcessDouyinURLFromText() unexpected error = %v", err)
				}
			}
		})
	}
}

// 测试包级别函数的向后兼容性
func TestProcessDouyinURLFromText_PackageLevel(t *testing.T) {
	tests := []struct {
		name        string
		text        string
		expectError bool
	}{
		{
			name:        "text without URLs",
			text:        "这是一段没有链接的文本",
			expectError: true,
		},
		{
			name:        "empty text",
			text:        "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, _, err := ProcessDouyinURLFromText(tt.text)
			if tt.expectError {
				if err == nil {
					t.Errorf("ProcessDouyinURLFromText() expected error for input: %v", tt.text)
				}
			} else {
				if err != nil {
					t.Errorf("ProcessDouyinURLFromText() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestExtractAwemeID(t *testing.T) {
	processor := NewURLProcessor()

	tests := []struct {
		name     string
		url      string
		expected string
	}{
		{
			name:     "valid douyin URL",
			url:      "https://www.douyin.com/video/7234567890123456789/",
			expected: "7234567890123456789",
		},
		{
			name:     "valid douyin URL without trailing slash",
			url:      "https://www.douyin.com/video/7234567890123456789",
			expected: "7234567890123456789",
		},
		{
			name:     "invalid URL",
			url:      "https://www.douyin.com/user/123456",
			expected: "",
		},
		{
			name:     "empty URL",
			url:      "",
			expected: "",
		},
		{
			name:     "valid douyin video URL",
			url:      "https://www.douyin.com/video/7123456789012345678",
			expected: "7123456789012345678",
		},
		{
			name:     "valid douyin video URL with trailing slash",
			url:      "https://www.douyin.com/video/7123456789012345678/",
			expected: "7123456789012345678",
		},
		{
			name:     "invalid URL without video path",
			url:      "https://www.douyin.com/channel/300203",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processor.ExtractAwemeID(tt.url)
			if result != tt.expected {
				t.Errorf("ExtractAwemeID() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestExtractURLsFromText(t *testing.T) {
	processor := NewURLProcessor()

	tests := []struct {
		name     string
		text     string
		expected []string
	}{
		{
			name:     "single URL",
			text:     "这是一个抖音视频链接：https://v.douyin.com/iFRvEaH/",
			expected: []string{"https://v.douyin.com/iFRvEaH/"},
		},
		{
			name:     "multiple URLs",
			text:     "链接1：https://v.douyin.com/iFRvEaH/ 链接2：https://www.douyin.com/video/123456789",
			expected: []string{"https://v.douyin.com/iFRvEaH/", "https://www.douyin.com/video/123456789"},
		},
		{
			name:     "no URLs",
			text:     "这是一段没有链接的文本",
			expected: []string{},
		},
		{
			name:     "text with single URL",
			text:     "Check this out: https://www.douyin.com/video/123",
			expected: []string{"https://www.douyin.com/video/123"},
		},
		{
			name:     "text with multiple URLs",
			text:     "Visit https://www.douyin.com and https://www.example.com",
			expected: []string{"https://www.douyin.com", "https://www.example.com"},
		},
		{
			name:     "text without URLs",
			text:     "This is just plain text",
			expected: []string{},
		},
		{
			name:     "empty text",
			text:     "",
			expected: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processor.ExtractURLsFromText(tt.text)
			if len(result) != len(tt.expected) {
				t.Errorf("ExtractURLsFromText() returned %d URLs, want %d", len(result), len(tt.expected))
				return
			}
			for i, url := range result {
				if url != tt.expected[i] {
					t.Errorf("ExtractURLsFromText() URL[%d] = %v, want %v", i, url, tt.expected[i])
				}
			}
		})
	}
}

// 集成测试：需要真实的网络请求
func TestConvertDouyinURL_Integration(t *testing.T) {
	// 跳过集成测试，除非设置了集成测试标志
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	processor := NewURLProcessor()

	tests := []struct {
		name     string
		shortURL string
		// 由于真实URL会变化，这里只测试是否能成功转换
		expectError bool
	}{
		{
			name:        "valid douyin short URL",
			shortURL:    "https://v.douyin.com/7xV8fLaUkek/",
			expectError: false,
		},
		{
			name:        "invalid URL",
			shortURL:    "https://invalid-url.com/test",
			expectError: false, // 即使是无效URL，也可能被重定向到其他页面
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.ConvertDouyinURL(tt.shortURL)
			if tt.expectError {
				if err == nil {
					t.Errorf("ConvertDouyinURL() expected error, but got result: %v", result)
				}
			} else {
				if err != nil {
					t.Errorf("ConvertDouyinURL() error = %v", err)
				}
				if result == "" {
					t.Errorf("ConvertDouyinURL() returned empty result")
				}
			}
		})
	}
}

func TestGetFinalRedirectURL(t *testing.T) {
	processor := NewURLProcessor()

	tests := []struct {
		name        string
		setupServer func() *httptest.Server
		expectedURL string
		expectError bool
	}{
		{
			name: "无重定向URL",
			setupServer: func() *httptest.Server {
				return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.WriteHeader(http.StatusOK)
				}))
			},
			expectError: false,
		},
		{
			name: "单次重定向",
			setupServer: func() *httptest.Server {
				return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					if r.URL.Path == "/redirect" {
						w.Header().Set("Location", "/final")
						w.WriteHeader(http.StatusFound)
					} else if r.URL.Path == "/final" {
						w.WriteHeader(http.StatusOK)
					}
				}))
			},
			expectError: false,
		},
		{
			name: "多次重定向",
			setupServer: func() *httptest.Server {
				return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					switch r.URL.Path {
					case "/start":
						w.Header().Set("Location", "/middle")
						w.WriteHeader(http.StatusFound)
					case "/middle":
						w.Header().Set("Location", "/final")
						w.WriteHeader(http.StatusFound)
					case "/final":
						w.WriteHeader(http.StatusOK)
					}
				}))
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := tt.setupServer()
			defer server.Close()

			var testURL string
			if tt.name == "无重定向URL" {
				testURL = server.URL
			} else if tt.name == "单次重定向" {
				testURL = server.URL + "/redirect"
			} else {
				testURL = server.URL + "/start"
			}

			finalURL, err := processor.GetFinalRedirectURL(testURL)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望有错误，但没有收到错误")
				}
			} else {
				if err != nil {
					t.Errorf("意外的错误: %v", err)
				}
				if finalURL == "" {
					t.Errorf("最终URL为空")
				}
			}
		})
	}
}

func TestCheckVideoUrlValidity(t *testing.T) {
	processor := NewURLProcessor()

	tests := []struct {
		name          string
		setupServer   func() *httptest.Server
		expectedValid bool
	}{
		{
			name: "有效的视频URL",
			setupServer: func() *httptest.Server {
				return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.Header().Set("Content-Type", "video/mp4")
					w.WriteHeader(http.StatusOK)
				}))
			},
			expectedValid: true,
		},
		{
			name: "无效的内容类型",
			setupServer: func() *httptest.Server {
				return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.Header().Set("Content-Type", "text/html")
					w.WriteHeader(http.StatusOK)
				}))
			},
			expectedValid: false,
		},
		{
			name: "错误的状态码",
			setupServer: func() *httptest.Server {
				return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.Header().Set("Content-Type", "video/mp4")
					w.WriteHeader(http.StatusNotFound)
				}))
			},
			expectedValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := tt.setupServer()
			defer server.Close()

			valid := processor.CheckVideoUrlValidity(server.URL)

			if valid != tt.expectedValid {
				t.Errorf("期望 %v, 得到 %v", tt.expectedValid, valid)
			}
		})
	}
}

func TestCheckVideoUrlValidityWithRedirect(t *testing.T) {
	processor := NewURLProcessor()

	// 创建一个带重定向的测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/redirect" {
			w.Header().Set("Location", "/video")
			w.WriteHeader(http.StatusFound)
		} else if r.URL.Path == "/video" {
			w.Header().Set("Content-Type", "video/mp4")
			w.WriteHeader(http.StatusOK)
		}
	}))
	defer server.Close()

	valid := processor.CheckVideoUrlValidityWithRedirect(server.URL + "/redirect")

	if !valid {
		t.Errorf("期望重定向后的视频URL有效")
	}
}
