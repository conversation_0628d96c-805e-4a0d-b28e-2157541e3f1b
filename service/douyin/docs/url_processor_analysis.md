# URLProcessor 服务分析文档

## 概述
`URLProcessor` 是一个专门用于处理抖音URL的服务。它提供了一系列工具方法，用于解析、转换和验证抖音相关的URL。该服务被设计为可重用的结构体，同时也提供了包级别的函数以便向后兼容。

## 功能模块
1.  **URL重定向解析**: 获取HTTP重定向后的最终URL。
2.  **视频URL有效性检查**: 验证一个URL是否指向一个有效的视频资源。
3.  **URL提取**: 从任意文本中提取所有符合格式的URL。
4.  **抖音链接转换**: 将抖音的短链接或分享链接转换为包含`aweme_id`的完整视频页链接。
5.  **Aweme ID提取**: 从完整的视频链接中解析出`aweme_id`。
6.  **一体化处理**: 提供一个从文本直接到`aweme_id`的完整处理流程。

## 流程图

### `GetFinalRedirectURL`
```mermaid
flowchart TD
    A[开始: GetFinalRedirectURL] --> B{初始化HTTP客户端 (禁用自动重定向)}
    B --> C[设置当前URL = 原始URL]
    C --> D{循环 (最多10次)}
    D --> E[创建HEAD请求]
    E --> F[设置请求头]
    F --> G[发送请求]
    G --> H{响应状态码是3xx?}
    H -->|是| I[获取Location头]
    I --> J{Location头为空?}
    J -->|是| K[返回当前URL]
    J -->|否| L[解析重定向URL]
    L --> M{是相对路径?}
    M -->|是| N[与当前URL合并]
    M -->|否| O[更新当前URL为重定向URL]
    N --> O
    O --> D
    H -->|否| P[返回当前URL]
    D -- 超过10次 --> Q[返回当前URL]
```

### `ProcessDouyinURLFromText`
```mermaid
flowchart TD
    A[开始: ProcessDouyinURLFromText] --> B[调用 ExtractURLsFromText]
    B --> C{找到URL?}
    C -->|否| D[返回错误: 未找到URL]
    C -->|是| E[取第一个URL]
    E --> F[调用 ConvertDouyinURL]
    F --> G{转换成功?}
    G -->|否| H[返回转换错误]
    G -->|是| I[获取fullURL]
    I --> J[调用 ExtractAwemeID]
    J --> K[获取awemeID]
    K --> L[返回 fullURL, awemeID, nil]
```

## 时序图

### `ConvertDouyinURL`
```mermaid
sequenceDiagram
    participant Caller as 调用方
    participant Processor as URLProcessor
    participant DouyinServer as 抖音服务器

    Caller->>Processor: ConvertDouyinURL(shortURL)
    Processor->>DouyinServer: 发送GET请求 (shortURL)
    DouyinServer-->>Processor: 响应 (302 Found, Location: longURL)
    Processor->>Processor: GetFinalRedirectURL(longURL)
    Processor-->>Caller: 返回 finalURL
```

## UML类图
```mermaid
classDiagram
    class URLProcessor {
        +GetFinalRedirectURL(originalURL string) (string, error)
        +CheckVideoUrlValidity(url string) bool
        +CheckVideoUrlValidityWithRedirect(originalURL string) bool
        +ExtractURLsFromText(text string) []string
        +ConvertDouyinURL(shortURL string) (string, error)
        +ExtractAwemeID(douyinURL string) string
        +ProcessDouyinURLFromText(text string) (string, string, error)
    }

    note for URLProcessor "提供包级别函数以向后兼容"
```

## 方法详解

### `GetFinalRedirectURL`
- **功能**: 手动处理HTTP重定向，获取最终的目标URL。
- **实现**: 使用`HEAD`请求以提高效率，避免下载响应体。通过`http.ErrUseLastResponse`禁用客户端的自动重定向，循环处理3xx状态码，直到获得非重定向响应或达到最大次数。
- **应用场景**: 处理短链接、追踪广告链接等。

### `CheckVideoUrlValidity`
- **功能**: 检查给定的URL是否指向一个有效的视频文件。
- **实现**: 发送`HEAD`请求，检查响应的`StatusCode`是否为200，并验证`Content-Type`头是否包含"video"、"mp4"或"octet-stream"。
- **局限**: 不能处理需要重定向的URL。

### `CheckVideoUrlValidityWithRedirect`
- **功能**: `CheckVideoUrlValidity`的增强版，支持重定向。
- **实现**: 先调用`GetFinalRedirectURL`获取最终URL，再调用`CheckVideoUrlValidity`进行检查。

### `ExtractURLsFromText`
- **功能**: 从一段文本中提取所有符合URL格式的字符串。
- **实现**: 使用正则表达式 `(https?:\/\/\w+[^\s]+\.[^\s]+)` 进行匹配。

### `ConvertDouyinURL`
- **功能**: 将抖音的分享短链接转换为包含`aweme_id`的完整长链接。
- **实现**: 发送`GET`请求到短链接，获取第一次重定向后的URL，然后调用`GetFinalRedirectURL`追踪到底。
- **关键点**: 模拟了浏览器行为，设置了`User-Agent`等请求头。

### `ExtractAwemeID`
- **功能**: 从抖音的完整视频URL中提取视频的唯一标识`aweme_id`。
- **实现**: 使用正则表达式`/video/(\d+)/?`匹配URL路径。

### `ProcessDouyinURLFromText`
- **功能**: 一站式处理函数，整合了URL提取、转换和ID提取的全过程。
- **实现**: 按顺序调用`ExtractURLsFromText`, `ConvertDouyinURL`, `ExtractAwemeID`。

## 兼容性设计
为了不破坏现有代码的调用方式，该文件在定义了`URLProcessor`结构体和其方法后，还提供了一组同名的包级别函数。这些函数内部实例化一个默认的`URLProcessor`并调用其方法。

```go
// 包级别变量，用于向后兼容
var defaultProcessor = NewURLProcessor()

// 包级别函数
func ProcessDouyinURLFromText(text string) (string, string, error) {
	return defaultProcessor.ProcessDouyinURLFromText(text)
}
```

## 错误处理
- **自定义错误**: 在逻辑分支（如未找到URL）中返回`errors.New`创建的错误。
- **原生错误**: 在HTTP请求、URL解析等失败时，直接向上传递返回的`error`。
- **日志记录**: 在`CheckVideoUrlValidity`等返回`bool`的函数中，使用`gf.Log()`记录内部错误详情，而不向外暴露。

## 依赖关系
- **标准库**: `context`, `errors`, `net/http`, `net/url`, `regexp`, `strings`, `time`
- **内部依赖**: `gofly/utils/gf` (主要用于日志)

## 改进建议
1.  **HTTP客户端复用**: `http.Client`是并发安全的，可以考虑在`URLProcessor`结构体中持有一个客户端实例，而不是在每个方法中创建，以提高性能和资源利用率。
2.  **正则表达式优化**: `regexp.MustCompile`的调用可以放在包初始化`init()`函数或全局变量中，避免在每次函数调用时都重新编译。
3.  **上下文传递**: 为所有执行网络请求的方法添加入参`context.Context`，以便更好地控制请求的生命周期（如超时和取消）。
4.  **配置化**: `Timeout`, `maxRedirects`, `User-Agent`等参数可以考虑从配置文件加载，增加灵活性。