package douyin

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"gofly/app/client/entity"
	"gofly/setting"
	"gofly/utils/gf"

	openapi_client "github.com/qihaozhushou/mediacrawler-client"
)

// URLProcessor 抖音URL处理服务
type URLProcessor struct{}

// NewURLProcessor 创建抖音URL处理服务实例
func NewURLProcessor() *URLProcessor {
	return &URLProcessor{}
}

// GetFinalRedirectURL 获取URL重定向后的最终链接，不获取响应体
func (p *URLProcessor) GetFinalRedirectURL(originalURL string) (string, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
		// 禁用自动重定向，手动处理
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}

	currentURL := originalURL
	maxRedirects := 10 // 最多跟随10次重定向

	for i := 0; i < maxRedirects; i++ {
		req, err := http.NewRequest("HEAD", currentURL, nil)
		if err != nil {
			return "", err
		}

		// 设置常用请求头，避免被反爬虫拦截
		req.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1")
		req.Header.Set("Referer", "https://www.douyin.com/")

		resp, err := client.Do(req)
		if err != nil {
			return "", err
		}
		defer resp.Body.Close()

		// 检查是否是重定向状态码
		if resp.StatusCode >= 300 && resp.StatusCode < 400 {
			location := resp.Header.Get("Location")
			if location == "" {
				// 没有Location头，返回当前URL
				return currentURL, nil
			}

			// 解析重定向URL
			redirectURL, err := url.Parse(location)
			if err != nil {
				return "", err
			}

			// 如果是相对URL，需要与当前URL合并
			if !redirectURL.IsAbs() {
				baseURL, err := url.Parse(currentURL)
				if err != nil {
					return "", err
				}
				redirectURL = baseURL.ResolveReference(redirectURL)
			}

			currentURL = redirectURL.String()
		} else {
			// 不是重定向状态码，返回当前URL
			return currentURL, nil
		}
	}

	// 超过最大重定向次数，返回最后的URL
	return currentURL, nil
}

// CheckVideoUrlValidity 检查视频URL是否有效
func (p *URLProcessor) CheckVideoUrlValidity(url string) bool {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	req, err := http.NewRequest("HEAD", url, nil)
	if err != nil {
		gf.Log().Error(context.Background(), "创建请求失败: %v", err)
		return false
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1")
	req.Header.Set("Referer", "https://www.douyin.com/")

	resp, err := client.Do(req)
	if err != nil {
		gf.Log().Error(context.Background(), "请求失败: %v", err)
		return false
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		gf.Log().Info(context.Background(), "状态码不是200: %d", resp.StatusCode)
		return false
	}

	// 检查Content-Type
	contentType := resp.Header.Get("Content-Type")
	if !strings.Contains(strings.ToLower(contentType), "video") &&
		!strings.Contains(strings.ToLower(contentType), "mp4") &&
		!strings.Contains(strings.ToLower(contentType), "octet-stream") {
		gf.Log().Info(context.Background(), "Content-Type不是视频类型: %s", contentType)
		return false
	}

	return true
}

// CheckVideoUrlValidityWithRedirect 检查视频URL是否有效（支持重定向）
func (p *URLProcessor) CheckVideoUrlValidityWithRedirect(originalURL string) bool {
	// 先获取最终的重定向URL
	finalURL, err := p.GetFinalRedirectURL(originalURL)
	if err != nil {
		gf.Log().Error(context.Background(), "获取重定向URL失败: %v", err)
		return false
	}

	// 使用最终URL检查视频有效性
	return p.CheckVideoUrlValidity(finalURL)
}

// ExtractURLsFromText 从文本中提取所有URL
func (p *URLProcessor) ExtractURLsFromText(text string) []string {
	urlPattern := regexp.MustCompile(`(https?:\/\/\w+[^\s]+\.[^\s]+)`)
	return urlPattern.FindAllString(text, -1)
}

// ConvertDouyinURL 将抖音短链接转换为完整链接
func (p *URLProcessor) ConvertDouyinURL(shortURL string) (string, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}

	headers := map[string]string{
		"User-Agent":      "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",
		"Accept":          "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
		"Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
	}

	req, err := http.NewRequest("GET", shortURL, nil)
	if err != nil {
		return "", err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 进行二次请求以获取最终URL
	finalURL, err := p.GetFinalRedirectURL(resp.Request.URL.String())
	if err != nil {
		return "", err
	}

	return finalURL, nil
}

// ExtractAwemeID 从抖音URL中提取aweme_id
func (p *URLProcessor) ExtractAwemeID(douyinURL string) string {
	pattern := regexp.MustCompile(`/video/(\d+)/?`)
	matches := pattern.FindStringSubmatch(douyinURL)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// ProcessDouyinURLFromText 从文本中提取URL并转换为抖音视频链接，获取aweme_id
func (p *URLProcessor) ProcessDouyinURLFromText(text string) (string, string, error) {
	// 从文本中提取URL
	urls := p.ExtractURLsFromText(text)
	if len(urls) == 0 {
		return "", "", errors.New("未找到有效的URL")
	}

	// 转换第一个URL为抖音完整链接
	fullURL, err := p.ConvertDouyinURL(urls[0])
	if err != nil {
		return "", "", err
	}

	// 提取aweme_id
	awemeID := p.ExtractAwemeID(fullURL)

	return fullURL, awemeID, nil
}

// GetVideoMetaInfo 从抖音URL获取完整的视频元数据
func (p *URLProcessor) GetVideoMetaInfo(douyinURL string) (*entity.DouyinAweme, string, error) {
	// 1. 处理URL，获取awemeID和完整的URL
	fullURL, awemeID, err := p.ProcessDouyinURLFromText(douyinURL)
	if err != nil {
		gf.Log().Info(context.Background(), "使用 ProcessDouyinURLFromText 处理抖音URL失败, 尝试直接提取: %v", err)
		// 如果处理失败，尝试直接提取
		awemeID = p.ExtractAwemeID(douyinURL)
		fullURL = douyinURL
	}

	// 2. 检查awemeID是否存在
	if awemeID == "" {
		err := fmt.Errorf("无法从URL中提取aweme_id: %s", douyinURL)
		gf.Log().Error(context.Background(), err)
		return nil, "", err
	}

	// 3. 调用MediaCrawler API触发爬取和入库
	var baseUrl, _ = gf.Cfg().Get(context.Background(), "mediaCrawler.default.baseUrl")
	cfg := openapi_client.NewConfiguration()
	cfg.Servers = openapi_client.ServerConfigurations{
		openapi_client.ServerConfiguration{
			URL: baseUrl.String(),
		},
	}
	client := openapi_client.NewAPIClient(cfg)
	apiService := client.APIAPI
	req := apiService.ProcessVideoId(context.Background(), awemeID)

	_, _, err = req.Execute()
	if err != nil {
		gf.Log().Error(context.Background(), "MediaCrawler API 请求失败: %v", err)
		return nil, "", err
	}

	// 4. 从数据库查询视频信息
	douyinAweme := &entity.DouyinAweme{}
	err = setting.CrawlerModel(douyinAweme).Where("aweme_id", awemeID).Scan(douyinAweme)
	if err != nil {
		gf.Log().Error(context.Background(), "从数据库查询DouyinAweme失败: %v", err)
		return nil, "", err
	}

	return douyinAweme, fullURL, nil
}

// 为了保持向后兼容，提供包级别的函数
var defaultProcessor = NewURLProcessor()

// GetVideoMetaInfo 包级别函数，用于向后兼容
func GetVideoMetaInfo(douyinURL string) (*entity.DouyinAweme, string, error) {
	return defaultProcessor.GetVideoMetaInfo(douyinURL)
}

// ProcessDouyinURLFromText 包级别函数，用于向后兼容
func ProcessDouyinURLFromText(text string) (string, string, error) {
	return defaultProcessor.ProcessDouyinURLFromText(text)
}

// ExtractAwemeID 包级别函数，用于向后兼容
func ExtractAwemeID(douyinURL string) string {
	return defaultProcessor.ExtractAwemeID(douyinURL)
}

// ExtractURLsFromText 包级别函数，用于向后兼容
func ExtractURLsFromText(text string) []string {
	return defaultProcessor.ExtractURLsFromText(text)
}

// ConvertDouyinURL 包级别函数，用于向后兼容
func ConvertDouyinURL(shortURL string) (string, error) {
	return defaultProcessor.ConvertDouyinURL(shortURL)
}

// GetFinalRedirectURL 包级别函数，用于向后兼容
func GetFinalRedirectURL(originalURL string) (string, error) {
	return defaultProcessor.GetFinalRedirectURL(originalURL)
}

// CheckVideoUrlValidity 包级别函数，用于向后兼容
func CheckVideoUrlValidity(url string) bool {
	return defaultProcessor.CheckVideoUrlValidity(url)
}

// CheckVideoUrlValidityWithRedirect 包级别函数，用于向后兼容
func CheckVideoUrlValidityWithRedirect(originalURL string) bool {
	return defaultProcessor.CheckVideoUrlValidityWithRedirect(originalURL)
}
