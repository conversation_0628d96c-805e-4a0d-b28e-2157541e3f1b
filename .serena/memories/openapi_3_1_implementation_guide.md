# OpenAPI 3.1 代码生成实施指南

## 1. 主流工具 OpenAPI 3.1 支持现状

### OpenAPI Generator (推荐)
- **版本要求**: v6.2.0+ (2022年7月开始支持 OpenAPI 3.1)
- **最新版本**: v7.x 全面支持 OpenAPI 3.1
- **支持特性**:
  - JSON Schema Draft 2020-12
  - 组合关键字 (oneOf, anyOf, allOf)
  - webhooks 定义
  - pathItems 引用
- **优势**: 成熟稳定，社区活跃，多语言支持

### oapi-codegen
- **版本要求**: v1.13.0+ (2023年开始实验性支持)
- **当前状态**: 部分支持，主要针对 Go 语言
- **限制**: 对某些 3.1 特性支持不完全

### go-swagger
- **状态**: 目前不支持 OpenAPI 3.1
- **维护状态**: 较少更新，不推荐用于 3.1 项目

### pb33f/libopenapi (Go 专用)
- **版本**: v0.15.0+
- **特性**: 专门为 OpenAPI 3.1 设计的 Go 库
- **能力**: 解析、验证、建模、代码生成

## 2. 推荐实施方案

### 方案一：OpenAPI Generator (推荐)

#### 安装和配置
```bash
# 通过 Docker 运行 (推荐)
docker run --rm -v "${PWD}:/local" openapitools/openapi-generator-cli:v7.3.0 generate \
  -i /local/spec/api.yaml \
  -g go \
  -o /local/generated/go-client \
  --additional-properties=packageName=client,moduleName=github.com/yourorg/api-client

# 或通过 npm 安装
npm install @openapitools/openapi-generator-cli -g
```

#### 配置文件示例 (openapi-generator-config.yaml)
```yaml
generatorName: go
inputSpec: ./spec/openapi-3.1.yaml
outputDir: ./generated/go-client
additionalProperties:
  packageName: client
  moduleName: github.com/qihaozhushou/api-client
  packageVersion: 1.0.0
  withGoCodegenComment: true
  generateInterfaces: true
  enumClassPrefix: true
  structPrefix: true
modelPackage: models
apiPackage: api
gitRepoId: qihaozhushou-api-client
gitUserId: qihaozhushou
```

### 方案二：pb33f/libopenapi (Go 原生)

#### 集成示例
```go
// go.mod
require github.com/pb33f/libopenapi v0.15.0

// 使用示例
package main

import (
    "github.com/pb33f/libopenapi"
    "github.com/pb33f/libopenapi/datamodel"
)

func generateClient(specBytes []byte) error {
    // 解析 OpenAPI 3.1 规范
    doc, err := libopenapi.NewDocumentFromWithConfiguration(specBytes, 
        &datamodel.DocumentConfiguration{
            AllowFileReferences: true,
            AllowRemoteReferences: true,
        })
    if err != nil {
        return err
    }
    
    // 构建数据模型
    model, errs := doc.BuildV3Model()
    if len(errs) > 0 {
        return errs[0]
    }
    
    // 自定义代码生成逻辑
    return generateGoClient(model)
}
```

## 3. 项目集成建议

### 目录结构
```
rpc/
├── openapi/
│   ├── specs/
│   │   └── mediacrawler-v3.1.yaml
│   ├── generated/
│   │   └── go-client/
│   └── config/
│       └── generator-config.yaml
└── mediacrawler/
    └── generated/ (现有的)
```

### 自动化脚本 (generate-client.sh)
```bash
#!/bin/bash
set -e

SPEC_FILE="rpc/openapi/specs/mediacrawler-v3.1.yaml"
OUTPUT_DIR="rpc/openapi/generated/go-client"
CONFIG_FILE="rpc/openapi/config/generator-config.yaml"

echo "Generating Go client from OpenAPI 3.1 spec..."

# 清理旧的生成文件
rm -rf $OUTPUT_DIR

# 使用 OpenAPI Generator
docker run --rm \
  -v "${PWD}:/local" \
  openapitools/openapi-generator-cli:v7.3.0 generate \
  -i /local/$SPEC_FILE \
  -c /local/$CONFIG_FILE \
  -o /local/$OUTPUT_DIR

echo "Client generation completed!"
echo "Generated files in: $OUTPUT_DIR"
```

### Makefile 集成
```makefile
.PHONY: generate-openapi-client
generate-openapi-client:
	@echo "Generating OpenAPI 3.1 Go client..."
	@./scripts/generate-client.sh

.PHONY: validate-openapi-spec
validate-openapi-spec:
	@echo "Validating OpenAPI 3.1 specification..."
	@docker run --rm -v "${PWD}:/local" \
		openapitools/openapi-generator-cli:v7.3.0 validate \
		-i /local/rpc/openapi/specs/mediacrawler-v3.1.yaml
```

## 4. 迁移策略

### 从 OpenAPI 3.0 升级到 3.1
1. **规范文件升级**:
   - 更新 `openapi: 3.1.0`
   - 调整 JSON Schema 语法
   - 利用新特性 (webhooks, pathItems 等)

2. **代码生成工具升级**:
   - 从 oapi-codegen 迁移到 openapi-generator
   - 更新构建脚本和 CI/CD 流程

3. **向后兼容性**:
   - 保持现有 API 接口不变
   - 新功能使用 3.1 特性
   - 逐步迁移现有端点

## 5. 最佳实践

### OpenAPI 3.1 特性利用
- 使用 `discriminator` 改进多态类型处理
- 利用 `unevaluatedProperties` 优化对象验证
- 使用 `prefixItems` 处理数组类型
- 通过 `$dynamicRef` 实现动态引用

### 代码生成优化
- 启用接口生成 (`generateInterfaces: true`)
- 使用结构体前缀避免命名冲突
- 配置适当的包名和模块名
- 启用枚举类型生成

### 质量保证
- 规范文件验证 (spectral, openapi-generator validate)
- 生成代码的单元测试
- 集成测试覆盖主要用例
- 文档生成和维护