# 起号助手API项目开发配置

## 代码编辑规范

### 1. 字符串使用规范

- 尽量少使用硬编码字符串
- 使用常量或配置文件定义字符串
- 便于变量替换时进行关联错误检查

### 2. 方法设计原则

- 保持小颗粒度，单一职责
- 每个方法都应配备单元测试
- 测试文件放在同目录下，命名为 `*_test.go`

### 3. 依赖管理

- 优先寻找优秀的第三方库
- 避免重复造轮子
- 确保依赖库的稳定性和维护活跃度

## 项目关键文件路径

### 配置文件

- 主配置: `resource/config.yaml`
- 生产配置: `resource/config.prod.yaml`
- 测试配置: `resource/config.test.yaml`

### 核心模块

- TrendInsight客户端: `app/client/trendinsight/index.go`
- 爬虫任务管理: `app/client/trendinsight/index.go` (CreateCrawlerTask方法)
- 实体定义: `app/client/entity/`
- 服务层: `app/client/entity/services/`

### 工具和工作流

- 路由管理: `utils/router/router.go`
- 数据库工具: `utils/gf/`
- Eino工作流: `eino_flow/`

## 常用开发命令

### 开发环境

```bash
# 热重载开发
air

# 手动运行
go run main.go

# 运行测试
go test ./...
```

### 依赖管理

```bash
# 安装依赖
go mod tidy

# 添加新依赖
go get -u package_name
```

### 数据库操作

- 使用GoFly框架的ORM：`gf.Model("table_name")`
- 配置在 `resource/config.yaml` 的 database 段
- 支持多数据库配置（default, media_crawler）

## 当前重点功能

1. **爬虫任务管理**: CreateCrawlerTask 方法实现关键词监控
2. **用户关键词管理**: 支持用户自定义关键词和优先级
3. **TrendInsight API**: 视频搜索、创作者搜索
4. **数据解析**: 多层级API响应数据解析和结构化

## 注意事项

- 生产环境禁止某些危险操作
- 所有API都需要登录验证
- 支持RBAC权限控制
- 使用JWT进行身份验证
