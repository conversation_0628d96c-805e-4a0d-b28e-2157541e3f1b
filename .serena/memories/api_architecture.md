# API接口映射和架构文档

## 主要API端点

### TrendInsight 模块 (`app/client/trendinsight/`)

#### 搜索功能
- `POST /client/trendinsight/searchVideos` - 搜索巨量引擎平台视频
- `POST /client/trendinsight/searchCreators` - 搜索巨量引擎平台创作者

#### 爬虫任务管理
- `POST /client/trendinsight/createCrawlerTask` - 创建爬虫任务
- `GET /client/trendinsight/getUserKeywordList` - 获取用户关键词监控列表
- `DELETE /client/trendinsight/deleteUserKeyword` - 删除用户关键词关联

### 业务模块 (`app/business/`)

#### 开发者工具
- `app/business/developer/` - 代码生成、安装、卸载
- `app/business/datacenter/` - 数据中心配置管理

#### 用户管理
- `app/business/user/` - 用户权限、菜单管理

### 管理后台 (`app/admin/`)

#### 系统配置
- `app/admin/datacenter/configuration` - 系统配置管理

### 公共模块 (`app/common/`)

#### 系统安装
- `GET /common/install/index` - 系统安装页面
- `POST /common/install/save` - 执行系统安装

## 数据库结构

### 主数据库 (qihaozhushou)
- 用户相关表
- 业务逻辑表
- 权限管理表

### 媒体爬虫数据库 (media_crawler)
- 爬虫任务表
- 关键词表
- 用户关键词关联表
- 抓取数据存储表

## 关键服务组件

### 实体服务 (`app/client/entity/services/`)
- `CrawlerTaskService` - 爬虫任务管理
- `CrawlerKeywordService` - 关键词管理
- `UserCrawlerKeywordService` - 用户关键词关联管理

### 工作流引擎 (`eino_flow/`)
- 基于Eino的复杂业务流程编排
- 支持Lambda函数和状态管理

### 外部服务集成
- **MediaCrawler**: 媒体爬虫服务
- **TrendInsight**: 巨量引擎API包装器
- **火山引擎**: 视频处理服务
- **Coze AI**: AI分析工作流

## 配置管理

### 环境配置
- `runEnv`: debug/release - 运行环境
- `port`: 服务端口
- `mainurl`: 主域名

### 数据库配置
- `database.default`: 主数据库连接
- `database.media_crawler`: 爬虫数据库连接

### 第三方服务配置
- `mediaCrawler.default.baseUrl`: MediaCrawler服务地址
- `wechat.*`: 微信相关配置
- `pay.*`: 支付相关配置

## 权限和认证

### 登录验证
- `NoNeedLogin`: 无需登录的接口列表
- JWT Token验证

### RBAC权限控制
- `NoNeedAuths`: 无需权限验证的接口列表
- 基于角色的访问控制

## 开发模式

### 热重载开发
- 使用Air工具进行热重载
- 配置文件: `runner.conf`

### 调试工具
- pprof性能分析
- 访问地址: `http://127.0.0.1:8081/debug/pprof/`

## 部署架构

### 容器化部署
- `Dockerfile`: 容器构建配置
- `docker-compose.yml`: 本地开发环境

### 阿里云函数计算
- 支持Function Compute部署
- 触发器配置和事件处理
