# 当前开发任务和重点

## 当前正在开发的功能

### TrendInsight 客户端优化

基于当前代码 (`app/client/trendinsight/index.go`)，主要关注以下功能：

#### 1. 爬虫任务管理 (CreateCrawlerTask)

**当前实现状态**: 已完成基础功能

**核心逻辑**:

- 验证用户登录状态和参数有效性
- 支持多关键词批量任务创建
- 智能任务复用：检查已存在的活跃任务，避免重复创建
- 用户关键词关联：自动建立用户与关键词的关联关系
- 优先级管理：支持用户自定义关键词优先级

**关键代码段** (lines 548-581):

```go
// 参数验证
platform, ok := param["platform"]
crawlerType, ok := param["crawler_type"] 
keywords, ok := param["keywords"]

// 类型转换和验证
platformStr := platform.(string)
crawlerTypeStr := crawlerType.(string)
keywordsSlice, ok := keywords.([]interface{})

// 转换为字符串数组
var keywordStrs []string
for _, kw := range keywordsSlice {
    if kwStr, ok := kw.(string); ok {
        keywordStrs = append(keywordStrs, kwStr)
    }
}
```

#### 2. 用户关键词列表管理 (GetUserKeywordList)

**功能**: 获取用户关键词监控列表，支持分页和状态过滤

**特点**:

- 分页支持 (page, page_size)
- 状态过滤 (status)
- 包含关键词详细信息和用户自定义配置
- 显示名称优先使用用户别名

#### 3. 关键词删除管理 (DeleteUserKeyword)

**功能**: 软删除用户关键词关联

**安全特性**:

- 用户权限验证：只能删除自己的关键词
- UUID验证：通过UUID进行精确定位
- 软删除：不直接删除数据，标记为删除状态

## 需要重点关注的代码质量问题

### 1. 字符串硬编码问题

**当前问题**: 代码中存在大量硬编码字符串

**需要改进的地方**:

```go
// 错误示例 - 硬编码字符串
gf.Failed().SetMsg("爬虫类型不能为空").Regin(c)
gf.Failed().SetMsg("关键词列表不能为空").Regin(c)

// 建议改进 - 使用常量
const (
    ErrCrawlerTypeRequired = "爬虫类型不能为空"
    ErrKeywordsRequired = "关键词列表不能为空"
)
```

### 2. 方法粒度优化

**当前状态**: CreateCrawlerTask 方法过长 (约200行)

**建议拆分**:

- `validateCreateTaskParams()` - 参数验证
- `processKeywords()` - 关键词处理
- `createOrAssociateTasks()` - 任务创建或关联
- `buildTaskResponse()` - 响应数据构建

### 3. 单元测试覆盖

**当前状态**: 缺少单元测试

**需要添加的测试文件**:

- `index_test.go` - 主要方法的单元测试
- 测试用例覆盖：正常流程、异常处理、边界条件

## 第三方依赖使用情况

### 优秀的第三方库应用

1. **Gin框架**: Web服务框架
2. **GoFly**: 基础框架和工具集
3. **oapi-codegen**: OpenAPI客户端代码生成
4. **samber/lo**: 函数式编程工具
5. **redis/go-redis**: Redis客户端

### 建议引入的库

1. **testify**: 测试断言库
2. **mock**: 测试Mock工具
3. **validator**: 参数验证库

## 下一步开发计划

### 短期目标 (1-2周)

1. **代码重构**: 拆分大方法，减少硬编码字符串
2. **单元测试**: 为核心方法添加测试覆盖
3. **错误处理**: 统一错误码和错误信息管理

### 中期目标 (1个月)

1. **性能优化**: 数据库查询优化，缓存策略
2. **功能增强**: 批量操作支持，任务状态监控
3. **API文档**: OpenAPI规范文档完善

### 长期目标 (3个月)

1. **监控告警**: 任务失败自动重试，状态监控
2. **数据分析**: 关键词热度分析，趋势预测
3. **用户体验**: 实时通知，操作日志
