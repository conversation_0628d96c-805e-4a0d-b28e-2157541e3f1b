# 起号助手API项目分析总结

## 项目概述
- **项目名称**: 起号助手API (qihaozhushou-api)
- **技术栈**: Go 1.24.2 + Gin框架 + MySQL + Redis
- **架构**: 基于Gofly框架的微服务架构
- **主要功能**: 媒体内容爬取、数据分析、TrendInsight平台集成

## 核心模块

### 1. 客户端集成 (app/client)
- **TrendInsight客户端**: 巨量引擎平台API包装器
- **MediaCrawler集成**: 媒体爬虫服务接口
- **功能**: 视频搜索、创作者搜索、数据分析

### 2. 业务逻辑 (app/business & app/admin)
- 模块化设计，包含多个业务实体和控制器
- 支持管理后台和业务逻辑分离

### 3. 服务层 (service)
- **topic**: 主题相关工作流
- **assets**: 资产管理工作流
- **common**: 通用服务组件
- 集成Eino工作流引擎

### 4. 工具层 (utils)
- 路由管理、配置管理
- 数据库连接池
- 通用工具函数

## 技术特点
- **热重载**: 使用Air工具支持开发时热重载
- **容器化**: Docker部署支持
- **CI/CD**: GitHub Actions自动化部署
- **监控**: 支持pprof性能分析
- **跨域**: 完整的CORS配置
- **中间件**: 限流、JWT验证、错误处理

## 部署环境
- **开发环境**: 本地开发 + Air热重载
- **测试环境**: Docker容器 + 阿里云FC
- **生产环境**: Docker容器化部署