# GoFly ORM 使用规则

## 基本概念

GoFly ORM 采用了简化的关联设计，不同于其他 ORM 框架常见的 BelongsTo、HasOne、HasMany、ManyToMany 这样的模型关联设计。GoFly 框架倾向于简化设计，使模型关联查询更易于理解和使用。<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>

## 关联关系类型

GoFly ORM 支持以下关联关系：<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>

- **1:1 关系**：例如用户表与用户详情表
- **1:N 关系**：例如用户表与用户学分表
- **N:N 关系**：处理方式与 1:N 类似，只是多了一次关联或查询

## 数据模型定义

数据模型应根据表定义，例如：<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>

```go
// EntityUser 对应用户表
type EntityUser struct {
    // 字段定义
}

// EntityUserDetail 对应用户详情表
type EntityUserDetail struct {
    // 字段定义
}

// EntityUserScores 对应用户学分表
type EntityUserScores struct {
    // 字段定义
}

// Entity 组合模型，包含用户所有详细信息
type Entity struct {
    User       *EntityUser
    UserDetail *EntityUserDetail
    UserScores []*EntityUserScores
}
```

## 数据操作

### 数据写入

写入数据时涉及到简单的数据库事务。<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>

### 数据查询

#### 单条数据记录查询

使用 `Scan` 方法进行单条数据查询，该方法会自动识别绑定查询结果到单个对象属性或数组对象属性中。<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>

```go
// 示例
db.Model("user").Where("id", 1).Scan(&user)
```

#### 多条数据记录查询

使用 `ScanList` 方法进行多条数据查询，该方法需要指定结果字段与模型属性的关系。<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>

```go
// 示例
db.Model("user").ScanList(&users, "User")
```

## 重要方法

### ScanList 方法

`ScanList` 方法用于将查询到的数组数据绑定到指定的列表上：<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>

- `ScanList(&users, "User")`：将查询到的用户信息数组数据绑定到 users 列表中每一项的 User 属性上
- `ScanList(&users, "UserDetail", "User", "uid:Uid")`：将查询到的用户详情数组数据绑定到 users 列表中每一项的 UserDetail 属性上，并通过 uid:Uid 的字段:属性关联与 User 对象属性关联
- `ScanList(&users, "UserScores", "User", "uid:Uid")`：将查询到的用户学分数组数据绑定到 users 列表中每一项的 UserScores 属性上，由于 UserScores 是数组类型，方法内部会自动识别 1:N 的关系

### ListItemValues/ListItemValuesUnique 方法

这两个方法用于从列表中获取指定属性/键名的数据值，构造成数组返回：<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>

- `ListItemValuesUnique` 过滤重复的返回值，保证返回的列表数据中不带有重复值
- `gdb.ListItemValuesUnique(users, "Uid")`：获取 users 数组中每一个 Uid 属性，构造成 `[]interface{}` 数组返回
- `gdb.ListItemValuesUnique(users, "User", "Uid")`：获取 users 数组中每一个 User 属性项中的 Uid 属性，构造成 `[]interface{}` 数组返回

## 注意事项

- 如果关联数据中对应的关联属性数据不存在，该属性不会被初始化并将保持 nil<mcreference link="https://doc.goflys.cn/docview?id=26&fid=453" index="0">0</mcreference>
- 关联查询时需要正确指定字段与属性的映射关系
- 在处理 1:N 或 N:N 关系时，需要确保目标属性为数组类型

## 最佳实践

1. 保持模型结构简单，避免过度复杂的关联关系
2. 使用组合模型来表示完整的数据结构
3. 合理使用事务来保证数据一致性
4. 利用 `ScanList` 方法的关联功能减少手动数据绑定的工作

        当前模型请求量过大，请求排队约 1 位，请稍候或切换至其他模型问答体验更流畅。

## 要求
1. 如果存在 app/client/entity/base.go 中已存在的字段 则继承 BaseEntity
2. 字段要补齐 gorm 相关信息