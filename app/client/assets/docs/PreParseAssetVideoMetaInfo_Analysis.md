# PreParseAssetVideoMetaInfo 函数分析文档

## 概述
`PreParseAssetVideoMetaInfo` 函数是资产预处理流程中的核心函数，负责从抖音URL中提取视频元数据信息并填充到资产对象中。

## 函数签名
```go
func PreParseAssetVideoMetaInfo(asset *entity.Assets) error
```

## 功能描述
该函数实现了从抖音URL到完整视频元数据的转换流程，包括：
1. URL解析和aweme_id提取
2. 通过MediaCrawler API获取视频信息
3. 从数据库查询详细的视频数据
4. 填充资产对象的各项属性

## 流程图

```mermaid
flowchart TD
    A[开始: PreParseAssetVideoMetaInfo] --> B[调用 ProcessDouyinURLFromText]
    B --> C{URL处理成功?}
    C -->|是| D[获取 fullURL 和 awemeID]
    C -->|否| E[记录信息日志]
    E --> F[调用 ExtractAwemeID 直接提取]
    F --> G[使用原始URL作为fullURL]
    D --> H{awemeID 存在?}
    G --> H
    H -->|否| I[记录错误日志]
    I --> J[返回错误: 无法提取aweme_id]
    H -->|是| K[获取MediaCrawler配置]
    K --> L[创建OpenAPI客户端配置]
    L --> M[创建API客户端和服务]
    M --> N[调用 ProcessVideoId API]
    N --> O{API调用成功?}
    O -->|否| P[记录错误日志]
    P --> Q[返回API错误]
    O -->|是| R[从数据库查询DouyinAweme]
    R --> S[填充asset对象属性]
    S --> T[返回 nil - 成功]
```

## 时序图

```mermaid
sequenceDiagram
    participant Caller as 调用方
    participant Function as PreParseAssetVideoMetaInfo
    participant Utils as client_utils
    participant Config as gf.Cfg
    participant API as MediaCrawler API
    participant DB as 数据库

    Caller->>Function: 调用函数(asset)
    Function->>Utils: ProcessDouyinURLFromText(asset.DouyinURL)
    Utils-->>Function: fullURL, awemeID, err
    
    alt URL处理失败
        Function->>Utils: ExtractAwemeID(asset.DouyinURL)
        Utils-->>Function: awemeID
        Function->>Function: fullURL = asset.DouyinURL
    end
    
    alt awemeID为空
        Function-->>Caller: 返回错误
    end
    
    Function->>Config: 获取mediaCrawler.default.baseUrl
    Config-->>Function: baseUrl
    
    Function->>Function: 创建OpenAPI客户端配置
    Function->>API: ProcessVideoId(awemeID)
    API-->>Function: 响应
    
    alt API调用失败
        Function-->>Caller: 返回API错误
    end
    
    Function->>DB: 查询DouyinAweme(aweme_id=awemeID)
    DB-->>Function: douyinAweme数据
    
    Function->>Function: 填充asset属性
    Function-->>Caller: 返回nil(成功)
```

## 类图 (UML)

```mermaid
classDiagram
    class Assets {
        +UUID string
        +DouyinURL string
        +VideoID string
        +VideoTitle string
        +Author string
        +PostTime time.Time
        +LikeCount uint
        +CommentCount uint
        +ShareCount uint
        +CollectCount uint
        +VideoSourceUrl string
    }
    
    class DouyinAweme {
        +AwemeID string
        +Title string
        +Nickname string
        +CreateTime int64
        +LikedCount string
        +CommentCount string
        +ShareCount string
        +CollectedCount string
        +VideoDownloadURL string
    }
    
    class ClientUtils {
        +ProcessDouyinURLFromText(text string) (string, string, error)
        +ExtractAwemeID(url string) string
    }
    
    class OpenAPIClient {
        +ProcessVideoId(ctx context.Context, awemeId string) APIRequest
    }
    
    class PreParseAssetVideoMetaInfoFunction {
        +PreParseAssetVideoMetaInfo(asset *Assets) error
    }
    
    PreParseAssetVideoMetaInfoFunction --> Assets : 修改
    PreParseAssetVideoMetaInfoFunction --> ClientUtils : 使用
    PreParseAssetVideoMetaInfoFunction --> OpenAPIClient : 调用
    PreParseAssetVideoMetaInfoFunction --> DouyinAweme : 查询
    Assets --> DouyinAweme : 数据来源
```

## 错误处理

### 1. URL解析错误
- **触发条件**: `ProcessDouyinURLFromText` 函数调用失败
- **处理方式**: 记录Info级别日志，尝试使用 `ExtractAwemeID` 直接提取
- **降级策略**: 使用原始URL作为fullURL

### 2. aweme_id提取失败
- **触发条件**: 经过两次尝试仍无法提取到有效的aweme_id
- **处理方式**: 记录Error级别日志并返回错误
- **错误信息**: "无法从URL中提取aweme_id: {URL}"

### 3. MediaCrawler API调用失败
- **触发条件**: `ProcessVideoId` API调用返回错误
- **处理方式**: 记录Error级别日志并返回API错误
- **影响**: 整个流程中断，无法获取视频数据

## 数据流转

### 输入数据
- `asset.DouyinURL`: 抖音视频链接（可能是短链接或完整链接）

### 中间数据
1. `fullURL`: 处理后的完整抖音链接
2. `awemeID`: 从URL中提取的视频唯一标识
3. `douyinAweme`: 从数据库查询的视频详细信息

### 输出数据（填充到asset对象）
- `VideoID`: 视频ID (aweme_id)
- `VideoTitle`: 视频标题
- `Author`: 作者昵称
- `PostTime`: 发布时间
- `LikeCount`: 点赞数
- `CommentCount`: 评论数
- `ShareCount`: 分享数
- `CollectCount`: 收藏数
- `VideoSourceUrl`: 视频下载地址
- `DouyinURL`: 处理后的完整URL

## 依赖关系

### 内部依赖
- `gofly/app/client/utils.ProcessDouyinURLFromText`
- `gofly/app/client/utils.ExtractAwemeID`
- `gofly/app/client/entity.Assets`
- `gofly/app/client/entity.DouyinAweme`
- `gofly/setting.CrawlerModel`

### 外部依赖
- `github.com/qihaozhushou/mediacrawler-client`: MediaCrawler OpenAPI客户端
- `gofly/utils/gf`: GoFly框架工具
- `gofly/utils/tools/gconv`: 类型转换工具

### 配置依赖
- `mediaCrawler.default.baseUrl`: MediaCrawler服务的基础URL

## 性能考虑

### 网络调用
- **API调用**: `ProcessVideoId` 是同步网络调用，可能存在延迟
- **建议**: 考虑添加超时控制和重试机制

### 数据库查询
- **查询条件**: 使用 `aweme_id` 作为查询条件
- **性能**: 需要确保 `aweme_id` 字段有索引

### 错误恢复
- **降级处理**: URL解析失败时有降级方案
- **容错性**: 具备一定的容错能力

## 使用示例

```go
// 创建资产对象
asset := &entity.Assets{
    UUID: "some-uuid",
    DouyinURL: "https://v.douyin.com/shortlink/",
}

// 调用预处理函数
err := PreParseAssetVideoMetaInfo(asset)
if err != nil {
    log.Printf("预处理失败: %v", err)
    return
}

// 此时asset对象已填充完整的视频元数据
log.Printf("视频标题: %s", asset.VideoTitle)
log.Printf("作者: %s", asset.Author)
log.Printf("点赞数: %d", asset.LikeCount)
```

## 注意事项

1. **函数是修改性的**: 直接修改传入的asset指针，而不是返回新对象
2. **错误处理**: 调用方需要检查返回的错误并进行相应处理
3. **数据一致性**: 确保MediaCrawler API调用成功后，数据库中确实存在对应的记录
4. **并发安全**: 函数本身是无状态的，支持并发调用

## 改进建议

1. **添加超时控制**: 为API调用添加context超时
2. **增加重试机制**: 对于临时性网络错误进行重试
3. **缓存机制**: 对于已处理过的aweme_id可以考虑缓存
4. **指标监控**: 添加调用成功率、响应时间等监控指标