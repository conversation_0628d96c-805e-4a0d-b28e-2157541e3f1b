package assets_mapper

import (
	"fmt"
	"gofly/app/client/entity"
	"gofly/utils/tools/gconv"
)

// FromDouyinAweme creates a new Asset entity from a DouyinAweme entity.
// Returns a new Assets struct with data populated from DouyinAweme.
func FromDouyinAweme(douyinAweme *entity.DouyinAweme, fullURL string) *entity.Assets {
	return &entity.Assets{
		VideoID:        douyinAweme.AwemeID,
		VideoTitle:     douyinAweme.Title,
		Author:         douyinAweme.Nickname,
		PostTime:       douyinAweme.CreateTime,
		LikeCount:      uint(gconv.Int(douyinAweme.LikedCount)),
		CommentCount:   uint(gconv.Int(douyinAweme.CommentCount)),
		ShareCount:     uint(gconv.Int(douyinAweme.ShareCount)),
		CollectCount:   uint(gconv.Int(douyinAweme.CollectedCount)),
		VideoSourceUrl: douyinAweme.VideoDownloadURL,
		DouyinURL:      fullURL,
	}
}

// FromDouyinVideoInfo creates a new Asset entity from a DouyinVideoInfo entity.
// Returns a new Assets struct with data populated from DouyinVideoInfo.
func FromDouyinVideoInfo(videoInfo *entity.DouyinVideoInfo) *entity.Assets {
	return &entity.Assets{
		VideoID:        videoInfo.AwemeID,
		VideoTitle:     videoInfo.PreviewTitle,
		Author:         videoInfo.AuthorNickname,
		PostTime:       videoInfo.PostTime,
		LikeCount:      uint(videoInfo.Statistics.DiggCount),
		CommentCount:   uint(videoInfo.Statistics.CommentCount),
		ShareCount:     uint(videoInfo.Statistics.ShareCount),
		CollectCount:   uint(videoInfo.Statistics.CollectCount),
		VideoSourceUrl: videoInfo.VideoDownloadUrl,
		DouyinURL:      fmt.Sprintf("https://www.douyin.com/video/%s", videoInfo.AwemeID),
	}
}
