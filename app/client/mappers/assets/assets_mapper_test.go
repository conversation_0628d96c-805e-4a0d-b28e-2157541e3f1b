package assets_mapper

import (
	"gofly/app/client/entity"
	"testing"
	"time"

	"dario.cat/mergo"
)

func TestFromDouyinAweme(t *testing.T) {
	// 创建测试数据
	douyinAweme := &entity.DouyinAweme{
		AwemeID:          "7123456789012345678",
		Title:            "测试视频标题",
		Nickname:         "测试作者",
		CreateTime:       time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC), // 2022-01-01 00:00:00 UTC
		LikedCount:       "1000",
		CommentCount:     "100",
		ShareCount:       "50",
		CollectedCount:   "25",
		VideoDownloadURL: "https://example.com/video.mp4",
	}
	fullURL := "https://www.douyin.com/video/7123456789012345678"

	// 调用函数
	result := FromDouyinAweme(douyinAweme, fullURL)

	// 验证结果
	if result == nil {
		t.Fatal("FromDouyinAweme 返回了 nil")
	}

	if result.VideoID != douyinAweme.AwemeID {
		t.Errorf("VideoID 不匹配: 期望 %s, 实际 %s", douyinAweme.AwemeID, result.VideoID)
	}

	if result.VideoTitle != douyinAweme.Title {
		t.Errorf("VideoTitle 不匹配: 期望 %s, 实际 %s", douyinAweme.Title, result.VideoTitle)
	}

	if result.Author != douyinAweme.Nickname {
		t.Errorf("Author 不匹配: 期望 %s, 实际 %s", douyinAweme.Nickname, result.Author)
	}

	if result.LikeCount != 1000 {
		t.Errorf("LikeCount 不匹配: 期望 1000, 实际 %d", result.LikeCount)
	}

	if result.CommentCount != 100 {
		t.Errorf("CommentCount 不匹配: 期望 100, 实际 %d", result.CommentCount)
	}

	if result.ShareCount != 50 {
		t.Errorf("ShareCount 不匹配: 期望 50, 实际 %d", result.ShareCount)
	}

	if result.CollectCount != 25 {
		t.Errorf("CollectCount 不匹配: 期望 25, 实际 %d", result.CollectCount)
	}

	if result.VideoSourceUrl != douyinAweme.VideoDownloadURL {
		t.Errorf("VideoSourceUrl 不匹配: 期望 %s, 实际 %s", douyinAweme.VideoDownloadURL, result.VideoSourceUrl)
	}

	if result.DouyinURL != fullURL {
		t.Errorf("DouyinURL 不匹配: 期望 %s, 实际 %s", fullURL, result.DouyinURL)
	}
}

func TestMergoIntegration(t *testing.T) {
	// 测试与 mergo 的集成
	douyinAweme := &entity.DouyinAweme{
		AwemeID:          "7123456789012345678",
		Title:            "新的视频标题",
		Nickname:         "新的作者",
		CreateTime:       time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC),
		LikedCount:       "2000",
		CommentCount:     "200",
		ShareCount:       "100",
		CollectedCount:   "50",
		VideoDownloadURL: "https://example.com/new-video.mp4",
	}
	fullURL := "https://www.douyin.com/video/7123456789012345678"

	// 创建现有的 asset
	existingAsset := &entity.Assets{
		VideoID:        "7123456789012345678",
		VideoTitle:     "旧的视频标题",
		Author:         "旧的作者",
		PostTime:       time.Now().Add(-24 * time.Hour), // 一天前
		LikeCount:      1000,
		CommentCount:   100,
		ShareCount:     50,
		CollectCount:   25,
		VideoSourceUrl: "https://example.com/old-video.mp4",
		DouyinURL:      "https://www.douyin.com/video/7123456789012345678",
		// 这些字段应该保持不变
		UserUUID:         "user-123",
		Source:           "manual",
		AnalysisStatus:   "pending",
		GenerationStatus: "pending",
	}

	// 使用 mapper 创建新数据
	newAssetData := FromDouyinAweme(douyinAweme, fullURL)

	// 使用 mergo 合并数据
	err := mergo.Merge(existingAsset, newAssetData, mergo.WithOverride)
	if err != nil {
		t.Fatalf("mergo.Merge 失败: %v", err)
	}

	// 验证合并结果
	// 这些字段应该被更新
	if existingAsset.VideoTitle != "新的视频标题" {
		t.Errorf("VideoTitle 未更新: 期望 '新的视频标题', 实际 '%s'", existingAsset.VideoTitle)
	}

	if existingAsset.Author != "新的作者" {
		t.Errorf("Author 未更新: 期望 '新的作者', 实际 '%s'", existingAsset.Author)
	}

	if existingAsset.LikeCount != 2000 {
		t.Errorf("LikeCount 未更新: 期望 2000, 实际 %d", existingAsset.LikeCount)
	}

	if existingAsset.VideoSourceUrl != "https://example.com/new-video.mp4" {
		t.Errorf("VideoSourceUrl 未更新: 期望 'https://example.com/new-video.mp4', 实际 '%s'", existingAsset.VideoSourceUrl)
	}

	// 这些字段应该保持不变
	if existingAsset.UserUUID != "user-123" {
		t.Errorf("UserUUID 被意外修改: 期望 'user-123', 实际 '%s'", existingAsset.UserUUID)
	}

	if existingAsset.Source != "manual" {
		t.Errorf("Source 被意外修改: 期望 'manual', 实际 '%s'", existingAsset.Source)
	}

	if existingAsset.AnalysisStatus != "pending" {
		t.Errorf("AnalysisStatus 被意外修改: 期望 'pending', 实际 '%s'", existingAsset.AnalysisStatus)
	}

	if existingAsset.GenerationStatus != "pending" {
		t.Errorf("GenerationStatus 被意外修改: 期望 'pending', 实际 '%s'", existingAsset.GenerationStatus)
	}
}

func TestFromDouyinVideoInfo(t *testing.T) {
	// 创建测试数据
	douyinVideoInfo := &entity.DouyinVideoInfo{
		AwemeID:          "7123456789012345678",
		PreviewTitle:     "测试视频标题",
		AuthorNickname:   "测试作者",
		PostTime:         time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC),
		VideoDownloadUrl: "https://example.com/video.mp4",
		Statistics: entity.Statistics{
			DiggCount:    1000,
			CommentCount: 100,
			ShareCount:   50,
			CollectCount: 25,
		},
	}

	// 调用函数
	result := FromDouyinVideoInfo(douyinVideoInfo)

	// 验证结果
	if result == nil {
		t.Fatal("FromDouyinVideoInfo 返回了 nil")
	}

	if result.VideoID != douyinVideoInfo.AwemeID {
		t.Errorf("VideoID 不匹配: 期望 %s, 实际 %s", douyinVideoInfo.AwemeID, result.VideoID)
	}

	if result.VideoTitle != douyinVideoInfo.PreviewTitle {
		t.Errorf("VideoTitle 不匹配: 期望 %s, 实际 %s", douyinVideoInfo.PreviewTitle, result.VideoTitle)
	}

	if result.Author != douyinVideoInfo.AuthorNickname {
		t.Errorf("Author 不匹配: 期望 %s, 实际 %s", douyinVideoInfo.AuthorNickname, result.Author)
	}

	if !result.PostTime.Equal(douyinVideoInfo.PostTime) {
		t.Errorf("PostTime 不匹配: 期望 %v, 实际 %v", douyinVideoInfo.PostTime, result.PostTime)
	}

	if result.LikeCount != 1000 {
		t.Errorf("LikeCount 不匹配: 期望 1000, 实际 %d", result.LikeCount)
	}

	if result.CommentCount != 100 {
		t.Errorf("CommentCount 不匹配: 期望 100, 实际 %d", result.CommentCount)
	}

	if result.ShareCount != 50 {
		t.Errorf("ShareCount 不匹配: 期望 50, 实际 %d", result.ShareCount)
	}

	if result.CollectCount != 25 {
		t.Errorf("CollectCount 不匹配: 期望 25, 实际 %d", result.CollectCount)
	}

	if result.VideoSourceUrl != douyinVideoInfo.VideoDownloadUrl {
		t.Errorf("VideoSourceUrl 不匹配: 期望 %s, 实际 %s", douyinVideoInfo.VideoDownloadUrl, result.VideoSourceUrl)
	}

	expectedURL := "https://www.douyin.com/video/7123456789012345678"
	if result.DouyinURL != expectedURL {
		t.Errorf("DouyinURL 不匹配: 期望 %s, 实际 %s", expectedURL, result.DouyinURL)
	}
}
