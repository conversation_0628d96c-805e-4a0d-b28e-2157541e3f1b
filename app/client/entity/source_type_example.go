package entity

import "fmt"

// ExampleSourceTypeUsage 展示如何使用新的 SourceType 枚举
func ExampleSourceTypeUsage() {
	// 创建不同类型的实体
	keywordEntity := &UserInboxSourceRelated{
		UserUUID:   "user123",
		SourceId:   "keyword456",
		SourceType: SourceTypeKeyword,
	}

	authorEntity := &UserInboxSourceRelated{
		UserUUID:   "user123",
		SourceId:   "author789",
		SourceType: SourceTypeAuthor,
	}

	collectEntity := &UserInboxSourceRelated{
		UserUUID:   "user123",
		SourceId:   "collect101",
		SourceType: SourceTypeCollect,
	}

	// 演示类型检查
	entities := []*UserInboxSourceRelated{keywordEntity, authorEntity, collectEntity}
	
	for i, entity := range entities {
		fmt.Printf("实体 %d:\n", i+1)
		fmt.Printf("  来源类型: %s\n", entity.SourceType)
		fmt.Printf("  是关键词类型: %t\n", entity.IsKeywordType())
		fmt.Printf("  是作者类型: %t\n", entity.IsAuthorType())
		fmt.Printf("  是收藏夹类型: %t\n", entity.IsCollectType())
		
		// 验证类型
		if err := entity.ValidateType(); err != nil {
			fmt.Printf("  验证错误: %v\n", err)
		} else {
			fmt.Printf("  验证通过\n")
		}
		fmt.Println()
	}

	// 演示获取所有可用类型
	fmt.Println("所有可用的来源类型:")
	for _, sourceType := range GetAllSourceTypes() {
		fmt.Printf("  - %s\n", sourceType)
	}
}

// CreateEntityWithType 根据字符串创建指定类型的实体
func CreateEntityWithType(userUUID, sourceId, typeStr string) (*UserInboxSourceRelated, error) {
	entity := &UserInboxSourceRelated{
		UserUUID: userUUID,
		SourceId: sourceId,
	}

	// 根据字符串设置类型
	switch typeStr {
	case "KEYWORD":
		entity.SourceType = SourceTypeKeyword
	case "AUTHOR":
		entity.SourceType = SourceTypeAuthor
	case "COLLECT":
		entity.SourceType = SourceTypeCollect
	default:
		return nil, fmt.Errorf("不支持的来源类型: %s", typeStr)
	}

	// 验证类型
	if err := entity.ValidateType(); err != nil {
		return nil, err
	}

	return entity, nil
}

// GetTypeDescription 获取类型的中文描述
func GetTypeDescription(sourceType SourceType) string {
	switch sourceType {
	case SourceTypeKeyword:
		return "关键词"
	case SourceTypeAuthor:
		return "作者"
	case SourceTypeCollect:
		return "收藏夹"
	default:
		return "未知类型"
	}
}
