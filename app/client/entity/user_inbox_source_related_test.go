package entity

import (
	"testing"
)

// TestSourceTypeEnum 测试 SourceType 枚举
func TestSourceTypeEnum(t *testing.T) {
	// 测试枚举常量
	if SourceTypeKeyword != "KEYWORD" {
		t.<PERSON><PERSON><PERSON>("Expected SourceTypeKeyword to be 'KEYWORD', got '%s'", SourceTypeKeyword)
	}
	if SourceTypeAuthor != "AUTHOR" {
		t.<PERSON><PERSON><PERSON>("Expected SourceTypeAuthor to be 'AUTHOR', got '%s'", SourceTypeAuthor)
	}
	if SourceTypeCollect != "COLLECT" {
		t.<PERSON><PERSON><PERSON>("Expected SourceTypeCollect to be 'COLLECT', got '%s'", SourceTypeCollect)
	}
}

// TestSourceTypeIsValid 测试 IsValid 方法
func TestSourceTypeIsValid(t *testing.T) {
	// 测试有效的类型
	validTypes := []SourceType{SourceTypeKeyword, SourceTypeAuthor, SourceTypeCollect}
	for _, st := range validTypes {
		if !st.IsValid() {
			t.<PERSON><PERSON><PERSON>("Expected %s to be valid", st)
		}
	}

	// 测试无效的类型
	invalidType := SourceType("INVALID")
	if invalidType.IsValid() {
		t.Errorf("Expected %s to be invalid", invalidType)
	}
}

// TestUserInboxSourceRelatedValidateType 测试 ValidateType 方法
func TestUserInboxSourceRelatedValidateType(t *testing.T) {
	// 测试有效类型
	validEntity := &UserInboxSourceRelated{SourceType: SourceTypeKeyword}
	if err := validEntity.ValidateType(); err != nil {
		t.Errorf("Expected no error for valid type, got: %v", err)
	}

	// 测试无效类型
	invalidEntity := &UserInboxSourceRelated{SourceType: SourceType("INVALID")}
	if err := invalidEntity.ValidateType(); err == nil {
		t.Error("Expected error for invalid type, got nil")
	}
}

// TestUserInboxSourceRelatedTypeCheckers 测试类型检查方法
func TestUserInboxSourceRelatedTypeCheckers(t *testing.T) {
	// 测试关键词类型
	keywordEntity := &UserInboxSourceRelated{SourceType: SourceTypeKeyword}
	if !keywordEntity.IsKeywordType() {
		t.Error("Expected IsKeywordType to return true for KEYWORD type")
	}
	if !keywordEntity.IsVideoType() { // 向后兼容性测试
		t.Error("Expected IsVideoType to return true for KEYWORD type (backward compatibility)")
	}
	if keywordEntity.IsAuthorType() {
		t.Error("Expected IsAuthorType to return false for KEYWORD type")
	}
	if keywordEntity.IsCollectType() {
		t.Error("Expected IsCollectType to return false for KEYWORD type")
	}

	// 测试作者类型
	authorEntity := &UserInboxSourceRelated{SourceType: SourceTypeAuthor}
	if authorEntity.IsKeywordType() {
		t.Error("Expected IsKeywordType to return false for AUTHOR type")
	}
	if !authorEntity.IsAuthorType() {
		t.Error("Expected IsAuthorType to return true for AUTHOR type")
	}
	if authorEntity.IsCollectType() {
		t.Error("Expected IsCollectType to return false for AUTHOR type")
	}

	// 测试收藏夹类型
	collectEntity := &UserInboxSourceRelated{SourceType: SourceTypeCollect}
	if collectEntity.IsKeywordType() {
		t.Error("Expected IsKeywordType to return false for COLLECT type")
	}
	if collectEntity.IsAuthorType() {
		t.Error("Expected IsAuthorType to return false for COLLECT type")
	}
	if !collectEntity.IsCollectType() {
		t.Error("Expected IsCollectType to return true for COLLECT type")
	}
}

// TestBeforeCreateDefaultValue 测试 BeforeCreate 方法的默认值设置
func TestBeforeCreateDefaultValue(t *testing.T) {
	entity := &UserInboxSourceRelated{}
	
	// 调用 BeforeCreate 方法
	err := entity.BeforeCreate()
	if err != nil {
		t.Errorf("Expected no error from BeforeCreate, got: %v", err)
	}

	// 检查默认值是否正确设置
	if entity.SourceType != SourceTypeKeyword {
		t.Errorf("Expected default SourceType to be %s, got %s", SourceTypeKeyword, entity.SourceType)
	}
}

// TestGetAllSourceTypes 测试获取所有来源类型
func TestGetAllSourceTypes(t *testing.T) {
	allTypes := GetAllSourceTypes()
	expectedTypes := []SourceType{SourceTypeKeyword, SourceTypeAuthor, SourceTypeCollect}
	
	if len(allTypes) != len(expectedTypes) {
		t.Errorf("Expected %d types, got %d", len(expectedTypes), len(allTypes))
	}
	
	for i, expectedType := range expectedTypes {
		if allTypes[i] != expectedType {
			t.Errorf("Expected type at index %d to be %s, got %s", i, expectedType, allTypes[i])
		}
	}
}
