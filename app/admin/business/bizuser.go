package business

import (
	"gofly/utils/gf"
	"gofly/utils/tools/gconv"
	"gofly/utils/tools/gmap"
	"gofly/utils/tools/grand"
	"gofly/utils/tools/gvar"
)

type Bizuser struct{ NoNeedAuths []string }

func init() {
	fpath := Bizuser{NoNeedAuths: []string{"getRole", "isaccountexist"}}
	gf.Register(&fpath, fpath)
}

// 获取成员列表
func (api *Bizuser) GetList(c *gf.GinCtx) {
	pageNo := gconv.Int(c.DefaultQuery("page", "1"))
	pageSize := gconv.Int(c.Default<PERSON>uery("pageSize", "10"))
	//搜索添条件
	param, _ := gf.RequestParam(c)
	whereMap := gmap.New()
	//过滤数据权限
	account_id, filter := gf.GetDataAuthor(c)
	if filter { //需要权限过滤
		whereMap.Set("account_id IN(?)", account_id)
	}
	if name, ok := param["name"]; ok && name != "" {
		whereMap.Set("name like ?", "%"+gconv.String(name)+"%")
	}
	if mobile, ok := param["mobile"]; ok && mobile != "" {
		whereMap.Set("mobile", mobile)
	}
	if status, ok := param["status"]; ok && status != "" {
		whereMap.Set("status", status)
	}
	if createtime, ok := param["createtime"]; ok && createtime != "" {
		datetime_arr := gf.SplitAndStr(gf.String(createtime), ",")
		whereMap.Set("createtime between ? and ?", gf.Slice{datetime_arr[0] + " 00:00", datetime_arr[1] + " 23:59"})
	}
	MDB := gf.Model("business_account").Where("main_account", 1).Where(whereMap)
	totalCount, _ := MDB.Clone().Count()
	list, err := MDB.Fields("id,status,name,username,avatar,tel,mobile,email,remark,remark,city,address,company,fileSize,createtime").Page(pageNo, pageSize).Order("id desc").Select()
	if err != nil {
		gf.Failed().SetMsg(err.Error()).Regin(c)
	} else {
		for _, val := range list {
			roleid, _ := gf.Model("business_auth_role_access").Where("uid", val["id"]).Array("role_id")
			rolename, _ := gf.Model("business_auth_role").WhereIn("id", roleid).Array("name")
			val["rolename"] = gvar.New(rolename)
			val["roleid"] = gvar.New(roleid)
			//头像
			if val["avatar"] == nil {
				val["avatar"] = gvar.New(gf.GetMainURLLocal() + "resource/uploads/static/avatar.png")
			}
		}
		gf.Success().SetMsg("获取全部列表").SetData(gf.Map{
			"page":     pageNo,
			"pageSize": pageSize,
			"total":    totalCount,
			"items":    list}).Regin(c)
	}
}

// 保存、编辑
func (api *Bizuser) Save(c *gf.GinCtx) {
	param, _ := gf.RequestParam(c)
	var f_id = gf.GetEditId(param["id"])
	var roleid []interface{}
	if param["roleid"] != nil {
		roleid = param["roleid"].([]interface{})
		delete(param, "roleid")
	}
	if param["password"] != nil && param["password"] != "" {
		salt := grand.Str("*********", 6)
		mdpass := param["password"].(string) + salt
		param["password"] = gf.Md5(mdpass)
		param["salt"] = salt
	}
	if param["avatar"] == "" {
		param["avatar"] = "resource/uploads/static/avatar.png"
	}
	if f_id == 0 {
		param["account_id"] = c.GetInt64("userID") //当前用户ID
		param["main_account"] = 1
		addId, err := gf.Model("business_account").Data(param).InsertAndGetId()
		if err != nil {
			gf.Failed().SetMsg("添加失败").SetData(err).Regin(c)
		} else {
			if addId != 0 {
				gf.Model("business_account").
					Data(map[string]interface{}{"business_id": addId}).
					Where("id", addId).
					Update()
			}
			//添加角色-多个
			appRoleAccess(roleid, addId)
			gf.Success().SetMsg("添加成功！").SetData(addId).Regin(c)
		}
	} else {
		res, err := gf.Model("business_account").Data(param).Where("id", f_id).Update()
		if err != nil {
			gf.Failed().SetMsg("更新失败").SetData(err).Regin(c)
		} else {
			//添加角色-多个
			if roleid != nil {
				appRoleAccess(roleid, f_id)
			}
			gf.Success().SetMsg("更新成功！").SetData(res).Regin(c)
		}
	}
}

// 更新状态
func (api *Bizuser) UpStatus(c *gf.GinCtx) {
	param, _ := gf.RequestParam(c)
	res2, err := gf.Model("business_account").Where("id", param["id"]).Data(map[string]interface{}{"status": param["status"]}).Update()
	if err != nil {
		gf.Failed().SetMsg("更新失败！").SetData(err).Regin(c)
	} else {
		msg := "更新成功！"
		if res2 == nil {
			msg = "暂无数据更新"
		}
		gf.Success().SetMsg(msg).SetData(res2).Regin(c)
	}
}

// 删除
func (api *Bizuser) Del(c *gf.GinCtx) {
	param, _ := gf.RequestParam(c)
	res2, err := gf.Model("business_account").WhereIn("id", param["ids"]).Delete()
	if err != nil {
		gf.Failed().SetMsg("删除失败").SetData(err).Regin(c)
	} else {
		gf.Success().SetMsg("删除成功！").SetData(res2).Regin(c)
	}
}

// 添加授权-工具
func appRoleAccess(roleids []interface{}, uid interface{}) {
	//批量提交
	gf.Model("business_auth_role_access").Where("uid", uid).Delete()
	save_arr := []map[string]interface{}{}
	for _, val := range roleids {
		marr := map[string]interface{}{"uid": uid, "role_id": val}
		save_arr = append(save_arr, marr)
	}
	gf.Model("business_auth_role_access").Data(save_arr).Insert()
}

// 获取账号信息
func (api *Bizuser) GetAccount(c *gf.GinCtx) {
	userID := c.GetInt64("userID") //当前用户ID
	data, _ := gf.Model("business_account").Where("id", userID).Find()
	gf.Success().SetMsg("获取账号信息").SetData(data).Regin(c)
}

// 判断账号是否存在
func (api *Bizuser) Isaccountexist(c *gf.GinCtx) {
	param, _ := gf.RequestParam(c)
	if param["id"] != nil {
		res1, err := gf.Model("business_account").Where("id !=", param["id"]).Where("username", param["username"]).Value("id")
		if err != nil {
			gf.Failed().SetMsg("验证失败").SetData(err).Regin(c)
		} else if res1 != nil {
			gf.Failed().SetMsg("账号已存在").SetData(err).Regin(c)
		} else {
			gf.Success().SetMsg("验证通过").SetData(res1).Regin(c)
		}
	} else {
		res2, err := gf.Model("business_account").Where("username", param["username"]).Value("id")
		if err != nil {
			gf.Failed().SetMsg("验证失败").SetData(err).Regin(c)
		} else if res2 != nil {
			gf.Failed().SetMsg("账号已存在").SetData(err).Regin(c)
		} else {
			gf.Success().SetMsg("验证通过").SetData(res2).Regin(c)
		}
	}
}

// 表单-选择角色
func (api *Bizuser) GetRole(c *gf.GinCtx) {
	menuList, _ := gf.Model("business_auth_role").Where("status", 0).Fields("id ,pid,name").Order("weigh asc").Select()
	max_role_id, _ := gf.Model("business_auth_role").Order("id asc").Value("pid")
	menuList = gf.GetMenuChildrenArray(menuList, gf.Int64(max_role_id), "pid")
	gf.Success().SetMsg("表单选择角色多选用数据").SetData(menuList).Regin(c)
}
