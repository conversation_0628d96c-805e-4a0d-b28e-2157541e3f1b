# 关键词总数统计功能增强

## 概述

根据用户需求，在现有的关键词监控列表接口中增加了每项关键词的总数统计功能。现在不仅统计每一项的当天数量，还统计当前用户每项的总数。

## 修改内容

### 1. 核心功能修改

**文件**: `app/client/trendinsight/index.go`

**修改位置**: `getUserKeywordsByType` 方法（第484-584行）

**主要变更**:
- 在原有的 `today_count`（当天数量）统计基础上，新增了 `total_count`（总数量）统计
- 保持了原有的调试日志功能，便于问题排查
- 优化了日志输出，区分总数查询和当天数量查询

### 2. API 文档更新

**新增 Swagger 文档注释**:
- `GetUserVideoKeywords`: 获取用户视频关键词监控列表
- `GetUserAuthorKeywords`: 获取用户作者关键词监控列表

**响应数据结构更新**:
```json
{
  "code": 200,
  "msg": "获取关键词监控列表成功",
  "data": {
    "list": [
      {
        "id": "关键词UUID",
        "source_id": "源ID",
        "user_uuid": "用户UUID",
        "type": "KEYWORD|AUTHOR",
        "display_name": "显示名称",
        "name": "关键词名称",
        "today_count": 25,    // 当天数量（原有字段）
        "total_count": 150,   // 总数量（新增字段）
        "created_at": "创建时间",
        "updated_at": "更新时间",
        "trend_keyword": {},  // 关键词详情（视频类型）
        "trend_author": {}    // 作者详情（作者类型）
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10,
    "total_pages": 1,
    "has_more": false
  }
}
```

### 3. 统计逻辑实现

#### 总数统计查询
```sql
SELECT COUNT(*) FROM user_inbox_video_related 
WHERE user_uuid = ? 
  AND source_id = ? 
  AND source_type = ? 
  AND is_deleted = 0
```

#### 当天数量统计查询
```sql
SELECT COUNT(*) FROM user_inbox_video_related 
WHERE user_uuid = ? 
  AND source_id = ? 
  AND source_type = ? 
  AND is_deleted = 0 
  AND create_time >= ? 
  AND create_time < ?
```

### 4. 测试验证

**测试文件**: `examples/test_keyword_total_count.go`

**测试内容**:
- 数据结构验证
- 字段存在性检查
- 数据类型验证
- API响应结构测试

**测试结果**: ✅ 所有测试通过

## 影响的接口

### 1. 获取用户视频关键词监控列表
- **路径**: `/client/trendinsight/index/GetUserVideoKeywords`
- **方法**: GET
- **新增字段**: `total_count`

### 2. 获取用户作者关键词监控列表
- **路径**: `/client/trendinsight/index/GetUserAuthorKeywords`
- **方法**: GET
- **新增字段**: `total_count`

## 向后兼容性

- ✅ 保持了所有原有字段不变
- ✅ 新增字段不影响现有客户端
- ✅ 原有的 `today_count` 逻辑完全保持不变
- ✅ 所有原有的调试和日志功能保持不变

## 性能考虑

- 每个关键词项目增加一次数据库查询（总数统计）
- 查询使用了索引字段（user_uuid, source_id, source_type, is_deleted）
- 对于大量关键词的用户，可能会增加响应时间，但查询是并发执行的

## 使用示例

### 前端展示
```javascript
// 获取关键词列表
const response = await api.get('/client/trendinsight/index/GetUserVideoKeywords');
const keywords = response.data.list;

keywords.forEach(keyword => {
  console.log(`关键词: ${keyword.display_name}`);
  console.log(`今日新增: ${keyword.today_count}`);
  console.log(`历史总数: ${keyword.total_count}`);  // 新增字段
});
```

### 数据分析
- `total_count`: 用于显示该关键词的历史累计数据量
- `today_count`: 用于显示该关键词今日的新增数据量
- 可以计算增长率: `(today_count / total_count) * 100%`

## 后续优化建议

1. **缓存优化**: 考虑对总数统计进行缓存，减少数据库查询
2. **批量查询**: 可以考虑使用一次查询获取所有关键词的统计数据
3. **分页优化**: 对于大量关键词的场景，可以考虑异步加载统计数据
4. **监控指标**: 添加查询性能监控，跟踪响应时间变化

## 总结

本次修改成功实现了用户需求，在保持向后兼容性的前提下，为关键词监控列表增加了总数统计功能。修改包括：

- ✅ 新增 `total_count` 字段统计每项关键词的总数据量
- ✅ 保持原有 `today_count` 字段统计当天数据量
- ✅ 更新了 API 文档注释
- ✅ 添加了完整的测试验证
- ✅ 保持了向后兼容性

用户现在可以同时查看每个关键词的当天数量和历史总数，更好地了解数据趋势和关键词表现。
