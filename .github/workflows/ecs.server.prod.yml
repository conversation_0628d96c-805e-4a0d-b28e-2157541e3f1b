name: Prod ECS Server Publish

on:
  #  workflow_run 必须在默认分支或主分支
  workflow_run:
    workflows:
      - Dev ECS Server Publish
    types:
      - completed
    branches:
      - main

env:
  SERVICE_NAME: 【正式环境】起号助手 API
  DOCKER_IMAGE_NAME: qihaozhushou-api
  DEVLOP_ENV: prod

jobs:
  version:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    uses: 0xTeams/reuse-workflows/.github/workflows/version.download.yml@main

  vars:
    needs: [version]
    runs-on: ubuntu-22.04
    outputs:
      service-name: ${{ env.SERVICE_NAME }}
      service-url: ${{ vars.TEST_MANAGEMENT_URL }}
      version: ${{ steps.get-version.outputs.version }}
    steps:
      - uses: kokoroX/get-version-action@main
        id: get-version

  approval:
    needs: [vars]
    runs-on: ubuntu-22.04
    steps:
      - uses: trstringer/manual-approval@v1
        timeout-minutes: 1
        with:
          issue-title: ${{ needs.vars.outputs.version }}
          secret: ${{ secrets.PAT_TOKEN }}
          approvers: frontend
          minimum-approvals: 1

  deploy:
    needs: [vars, approval]
    runs-on: ubuntu-latest
    environment: production
    env:
      APP_VERSION: ${{ needs.vars.outputs.version }}
    steps:
      - name: update docker-compose and rollout
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.PRIVATE_KEY }}
          script: |
            cd /app/qihaozhushou-api
            export APP_VERSION=${{ env.APP_VERSION }}
            docker rollout -f docker-compose.yml qihaozhushou-api__${{ env.DEVLOP_ENV }}


  feishu-success-notice:
    needs: [vars, deploy]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: ${{ needs.vars.outputs.service-name }}
      service-url: ${{ needs.vars.outputs.service-url }}
      environment-name: 钛动环境


  feishu-failure-notice:
    if: failure()
    needs: [vars, deploy]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: ${{ needs.vars.outputs.service-name }}
      version: ${{ needs.vars.outputs.version }}
      environment-name: 钛动环境