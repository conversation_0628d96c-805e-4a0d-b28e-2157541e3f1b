name: Dev ECS Server Publish

on:
  push:
    branches:
      - dev
      - main
      - version/**
      - feature/**

env:
  SERVICE_NAME: 【测试环境】起号助手 API
  DOCKER_IMAGE_NAME: qihaozhushou-api
  DEVLOP_ENV: test

jobs:
  version:
    uses: 0xTeams/neobio-reuse-workflows/.github/workflows/version.upload.yml@main

  vars:
    needs: [version]
    runs-on: ubuntu-22.04
    outputs:
      service-name: ${{ env.SERVICE_NAME }}
      service-url: ${{ vars.TEST_MANAGEMENT_URL }}
      version: ${{ steps.get-version.outputs.version }}
    steps:
      - uses: kokoroX/get-version-action@main
        id: get-version

  build:
    needs: [vars]
    runs-on: ubuntu-22.04
    environment: taidong
    env:
      CGO_ENABLED: 0
      GOOS: linux
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 16
          registry-url: https://registry.npmjs.org/
      - run: npm install -g @serverless-devs/s
      - uses: actions/setup-go@v4
        with:
          go-version: '1.24'
          check-latest: true
          cache-dependency-path: go.sum
      - run: CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o main main.go
      - name: Log in to Docker Hub
        uses: docker/login-action@f4ef78c080cd8ba55a85445d5b36e214a81df20a
        with:
          username: ${{ secrets.ALI_DOCKER_USERNAME }}
          password: ${{ secrets.ALI_DOCKER_PASSWORD }}
          registry: ${{ vars.ALI_DOCKER_REGISTRY }}
      - name: Build and push Docker image
        id: push
        uses: docker/build-push-action@3b5e8027fcad23fda98b2e3ac259d8d67585f671
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ vars.ALI_DOCKER_REGISTRY }}/${{vars.ALI_DOCKER_NAMESPACE}}/${{env.DOCKER_IMAGE_NAME}}:${{ needs.vars.outputs.version }}


  deploy:
    needs: [vars, build]
    runs-on: ubuntu-latest
    environment: testing
    env:
      APP_VERSION: ${{ needs.vars.outputs.version }}
    steps:
      - name: update docker-compose and rollout
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.PRIVATE_KEY }}
          script: |
            cd /app/qihaozhushou-api
            export APP_VERSION=${{ env.APP_VERSION }}
            docker rollout -f docker-compose.yml qihaozhushou-api__${{ env.DEVLOP_ENV }}


  feishu-success-notice:
    needs: [vars, deploy]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: ${{ needs.vars.outputs.service-name }}
      service-url: ${{ needs.vars.outputs.service-url }}
      environment-name: 钛动环境


  feishu-failure-notice:
    if: failure()
    needs: [vars, deploy]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: ${{ needs.vars.outputs.service-name }}
      version: ${{ needs.vars.outputs.version }}
      environment-name: 钛动环境