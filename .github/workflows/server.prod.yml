name: Prod FC Server Publish

on:
  push:
    branches:
      - backupfc

env:
  SERVICE_NAME: 【正式环境】起号助手 API
  DEVLOP_ENV: prod

jobs:
  vars:
    runs-on: ubuntu-22.04
    outputs:
      service-name: ${{ env.SERVICE_NAME }}
      service-url: ${{ vars.TEST_MANAGEMENT_URL }}
      version: ${{ steps.get-version.outputs.version }}
    steps:
      - uses: kokoroX/get-version-action@main
        id: get-version


  approval:
    needs: [vars]
    runs-on: ubuntu-22.04
    steps:
      - uses: trstringer/manual-approval@v1
        timeout-minutes: 1
        with:
          issue-title: ${{ needs.vars.outputs.version }}
          secret: ${{ secrets.PAT_TOKEN }}
          approvers: frontend
          minimum-approvals: 1

  serverless-devs-cd:
    needs: [vars, approval]
    runs-on: ubuntu-22.04
    environment: taidong
    env:
      CGO_ENABLED: 0
      GOOS: linux
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 16
          registry-url: https://registry.npmjs.org/
      - run: mv resource/config.${{ env.DEVLOP_ENV }}.yaml resource/config.yaml
      - run: npm install -g @serverless-devs/s
      - uses: actions/setup-go@v4
        with:
          go-version: '1.24'
          check-latest: true
          cache-dependency-path: go.sum
      - run: CGO_ENABLED=0 GOOS=linux go build -o gofly
      - run: CGO_ENABLED=0 GOOS=linux go build -o ./tmp/job ./job/main.go
      - run: s config add --AccessKeyID ${{secrets.ALIYUN_ACCESS_KEY_ID}} --AccessKeySecret ${{secrets.ALIYUN_ACCESS_KEY_SECRET}} -a tdid -f
      - run: s deploy -y --use-local -t s.${{ env.DEVLOP_ENV }}.yaml


  feishu-success-notice:
    needs: [vars, serverless-devs-cd]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: ${{ needs.vars.outputs.service-name }}
      service-url: ${{ needs.vars.outputs.service-url }}
      environment-name: 钛动环境


  feishu-failure-notice:
    if: failure()
    needs: [vars, serverless-devs-cd]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: ${{ needs.vars.outputs.service-name }}
      version: ${{ needs.vars.outputs.version }}
      environment-name: 钛动环境