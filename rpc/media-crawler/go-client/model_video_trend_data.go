/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the VideoTrendData type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &VideoTrendData{}

// VideoTrendData 视频趋势数据模型
type VideoTrendData struct {
	// 视频ID
	AwemeId string `json:"aweme_id"`
	// 趋势评分
	TrendScore float32 `json:"trend_score"`
	// 创建时间
	CreatedAt string `json:"created_at"`
	// 更新时间
	UpdatedAt string `json:"updated_at"`
	// 是否已删除
	IsDeleted *bool `json:"is_deleted,omitempty"`
}

type _VideoTrendData VideoTrendData

// NewVideoTrendData instantiates a new VideoTrendData object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewVideoTrendData(awemeId string, trendScore float32, createdAt string, updatedAt string) *VideoTrendData {
	this := VideoTrendData{}
	this.AwemeId = awemeId
	this.TrendScore = trendScore
	this.CreatedAt = createdAt
	this.UpdatedAt = updatedAt
	var isDeleted bool = false
	this.IsDeleted = &isDeleted
	return &this
}

// NewVideoTrendDataWithDefaults instantiates a new VideoTrendData object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewVideoTrendDataWithDefaults() *VideoTrendData {
	this := VideoTrendData{}
	var isDeleted bool = false
	this.IsDeleted = &isDeleted
	return &this
}

// GetAwemeId returns the AwemeId field value
func (o *VideoTrendData) GetAwemeId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.AwemeId
}

// GetAwemeIdOk returns a tuple with the AwemeId field value
// and a boolean to check if the value has been set.
func (o *VideoTrendData) GetAwemeIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.AwemeId, true
}

// SetAwemeId sets field value
func (o *VideoTrendData) SetAwemeId(v string) {
	o.AwemeId = v
}

// GetTrendScore returns the TrendScore field value
func (o *VideoTrendData) GetTrendScore() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.TrendScore
}

// GetTrendScoreOk returns a tuple with the TrendScore field value
// and a boolean to check if the value has been set.
func (o *VideoTrendData) GetTrendScoreOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.TrendScore, true
}

// SetTrendScore sets field value
func (o *VideoTrendData) SetTrendScore(v float32) {
	o.TrendScore = v
}

// GetCreatedAt returns the CreatedAt field value
func (o *VideoTrendData) GetCreatedAt() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value
// and a boolean to check if the value has been set.
func (o *VideoTrendData) GetCreatedAtOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CreatedAt, true
}

// SetCreatedAt sets field value
func (o *VideoTrendData) SetCreatedAt(v string) {
	o.CreatedAt = v
}

// GetUpdatedAt returns the UpdatedAt field value
func (o *VideoTrendData) GetUpdatedAt() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.UpdatedAt
}

// GetUpdatedAtOk returns a tuple with the UpdatedAt field value
// and a boolean to check if the value has been set.
func (o *VideoTrendData) GetUpdatedAtOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.UpdatedAt, true
}

// SetUpdatedAt sets field value
func (o *VideoTrendData) SetUpdatedAt(v string) {
	o.UpdatedAt = v
}

// GetIsDeleted returns the IsDeleted field value if set, zero value otherwise.
func (o *VideoTrendData) GetIsDeleted() bool {
	if o == nil || IsNil(o.IsDeleted) {
		var ret bool
		return ret
	}
	return *o.IsDeleted
}

// GetIsDeletedOk returns a tuple with the IsDeleted field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *VideoTrendData) GetIsDeletedOk() (*bool, bool) {
	if o == nil || IsNil(o.IsDeleted) {
		return nil, false
	}
	return o.IsDeleted, true
}

// HasIsDeleted returns a boolean if a field has been set.
func (o *VideoTrendData) HasIsDeleted() bool {
	if o != nil && !IsNil(o.IsDeleted) {
		return true
	}

	return false
}

// SetIsDeleted gets a reference to the given bool and assigns it to the IsDeleted field.
func (o *VideoTrendData) SetIsDeleted(v bool) {
	o.IsDeleted = &v
}

func (o VideoTrendData) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o VideoTrendData) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["aweme_id"] = o.AwemeId
	toSerialize["trend_score"] = o.TrendScore
	toSerialize["created_at"] = o.CreatedAt
	toSerialize["updated_at"] = o.UpdatedAt
	if !IsNil(o.IsDeleted) {
		toSerialize["is_deleted"] = o.IsDeleted
	}
	return toSerialize, nil
}

func (o *VideoTrendData) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"aweme_id",
		"trend_score",
		"created_at",
		"updated_at",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varVideoTrendData := _VideoTrendData{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varVideoTrendData)

	if err != nil {
		return err
	}

	*o = VideoTrendData(varVideoTrendData)

	return err
}

type NullableVideoTrendData struct {
	value *VideoTrendData
	isSet bool
}

func (v NullableVideoTrendData) Get() *VideoTrendData {
	return v.value
}

func (v *NullableVideoTrendData) Set(val *VideoTrendData) {
	v.value = val
	v.isSet = true
}

func (v NullableVideoTrendData) IsSet() bool {
	return v.isSet
}

func (v *NullableVideoTrendData) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableVideoTrendData(val *VideoTrendData) *NullableVideoTrendData {
	return &NullableVideoTrendData{value: val, isSet: true}
}

func (v NullableVideoTrendData) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableVideoTrendData) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


