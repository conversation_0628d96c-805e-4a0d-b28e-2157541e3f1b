/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the CookiesValidateResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CookiesValidateResponse{}

// CookiesValidateResponse Cookies验证响应模型（用于 cookies/validate 端点）
type CookiesValidateResponse struct {
	// Cookies是否有效
	Valid bool `json:"valid"`
	// 验证结果消息
	Message string `json:"message"`
}

type _CookiesValidateResponse CookiesValidateResponse

// NewCookiesValidateResponse instantiates a new CookiesValidateResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCookiesValidateResponse(valid bool, message string) *CookiesValidateResponse {
	this := CookiesValidateResponse{}
	this.Valid = valid
	this.Message = message
	return &this
}

// NewCookiesValidateResponseWithDefaults instantiates a new CookiesValidateResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCookiesValidateResponseWithDefaults() *CookiesValidateResponse {
	this := CookiesValidateResponse{}
	return &this
}

// GetValid returns the Valid field value
func (o *CookiesValidateResponse) GetValid() bool {
	if o == nil {
		var ret bool
		return ret
	}

	return o.Valid
}

// GetValidOk returns a tuple with the Valid field value
// and a boolean to check if the value has been set.
func (o *CookiesValidateResponse) GetValidOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Valid, true
}

// SetValid sets field value
func (o *CookiesValidateResponse) SetValid(v bool) {
	o.Valid = v
}

// GetMessage returns the Message field value
func (o *CookiesValidateResponse) GetMessage() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Message
}

// GetMessageOk returns a tuple with the Message field value
// and a boolean to check if the value has been set.
func (o *CookiesValidateResponse) GetMessageOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Message, true
}

// SetMessage sets field value
func (o *CookiesValidateResponse) SetMessage(v string) {
	o.Message = v
}

func (o CookiesValidateResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CookiesValidateResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["valid"] = o.Valid
	toSerialize["message"] = o.Message
	return toSerialize, nil
}

func (o *CookiesValidateResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"valid",
		"message",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varCookiesValidateResponse := _CookiesValidateResponse{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varCookiesValidateResponse)

	if err != nil {
		return err
	}

	*o = CookiesValidateResponse(varCookiesValidateResponse)

	return err
}

type NullableCookiesValidateResponse struct {
	value *CookiesValidateResponse
	isSet bool
}

func (v NullableCookiesValidateResponse) Get() *CookiesValidateResponse {
	return v.value
}

func (v *NullableCookiesValidateResponse) Set(val *CookiesValidateResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableCookiesValidateResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableCookiesValidateResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCookiesValidateResponse(val *CookiesValidateResponse) *NullableCookiesValidateResponse {
	return &NullableCookiesValidateResponse{value: val, isSet: true}
}

func (v NullableCookiesValidateResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCookiesValidateResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


