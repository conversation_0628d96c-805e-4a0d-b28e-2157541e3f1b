/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
)

// checks if the RpcTrendinsightSchemasUserUserInfoResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &RpcTrendinsightSchemasUserUserInfoResponse{}

// RpcTrendinsightSchemasUserUserInfoResponse 用户信息查询响应
type RpcTrendinsightSchemasUserUserInfoResponse struct {
	Data NullableRpcTrendinsightSchemasUserUserInfo `json:"data,omitempty"`
	Message NullableString `json:"message,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _RpcTrendinsightSchemasUserUserInfoResponse RpcTrendinsightSchemasUserUserInfoResponse

// NewRpcTrendinsightSchemasUserUserInfoResponse instantiates a new RpcTrendinsightSchemasUserUserInfoResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewRpcTrendinsightSchemasUserUserInfoResponse() *RpcTrendinsightSchemasUserUserInfoResponse {
	this := RpcTrendinsightSchemasUserUserInfoResponse{}
	return &this
}

// NewRpcTrendinsightSchemasUserUserInfoResponseWithDefaults instantiates a new RpcTrendinsightSchemasUserUserInfoResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewRpcTrendinsightSchemasUserUserInfoResponseWithDefaults() *RpcTrendinsightSchemasUserUserInfoResponse {
	this := RpcTrendinsightSchemasUserUserInfoResponse{}
	return &this
}

// GetData returns the Data field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *RpcTrendinsightSchemasUserUserInfoResponse) GetData() RpcTrendinsightSchemasUserUserInfo {
	if o == nil || IsNil(o.Data.Get()) {
		var ret RpcTrendinsightSchemasUserUserInfo
		return ret
	}
	return *o.Data.Get()
}

// GetDataOk returns a tuple with the Data field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *RpcTrendinsightSchemasUserUserInfoResponse) GetDataOk() (*RpcTrendinsightSchemasUserUserInfo, bool) {
	if o == nil {
		return nil, false
	}
	return o.Data.Get(), o.Data.IsSet()
}

// HasData returns a boolean if a field has been set.
func (o *RpcTrendinsightSchemasUserUserInfoResponse) HasData() bool {
	if o != nil && o.Data.IsSet() {
		return true
	}

	return false
}

// SetData gets a reference to the given NullableRpcTrendinsightSchemasUserUserInfo and assigns it to the Data field.
func (o *RpcTrendinsightSchemasUserUserInfoResponse) SetData(v RpcTrendinsightSchemasUserUserInfo) {
	o.Data.Set(&v)
}
// SetDataNil sets the value for Data to be an explicit nil
func (o *RpcTrendinsightSchemasUserUserInfoResponse) SetDataNil() {
	o.Data.Set(nil)
}

// UnsetData ensures that no value is present for Data, not even an explicit nil
func (o *RpcTrendinsightSchemasUserUserInfoResponse) UnsetData() {
	o.Data.Unset()
}

// GetMessage returns the Message field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *RpcTrendinsightSchemasUserUserInfoResponse) GetMessage() string {
	if o == nil || IsNil(o.Message.Get()) {
		var ret string
		return ret
	}
	return *o.Message.Get()
}

// GetMessageOk returns a tuple with the Message field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *RpcTrendinsightSchemasUserUserInfoResponse) GetMessageOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Message.Get(), o.Message.IsSet()
}

// HasMessage returns a boolean if a field has been set.
func (o *RpcTrendinsightSchemasUserUserInfoResponse) HasMessage() bool {
	if o != nil && o.Message.IsSet() {
		return true
	}

	return false
}

// SetMessage gets a reference to the given NullableString and assigns it to the Message field.
func (o *RpcTrendinsightSchemasUserUserInfoResponse) SetMessage(v string) {
	o.Message.Set(&v)
}
// SetMessageNil sets the value for Message to be an explicit nil
func (o *RpcTrendinsightSchemasUserUserInfoResponse) SetMessageNil() {
	o.Message.Set(nil)
}

// UnsetMessage ensures that no value is present for Message, not even an explicit nil
func (o *RpcTrendinsightSchemasUserUserInfoResponse) UnsetMessage() {
	o.Message.Unset()
}

func (o RpcTrendinsightSchemasUserUserInfoResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o RpcTrendinsightSchemasUserUserInfoResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if o.Data.IsSet() {
		toSerialize["data"] = o.Data.Get()
	}
	if o.Message.IsSet() {
		toSerialize["message"] = o.Message.Get()
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *RpcTrendinsightSchemasUserUserInfoResponse) UnmarshalJSON(data []byte) (err error) {
	varRpcTrendinsightSchemasUserUserInfoResponse := _RpcTrendinsightSchemasUserUserInfoResponse{}

	err = json.Unmarshal(data, &varRpcTrendinsightSchemasUserUserInfoResponse)

	if err != nil {
		return err
	}

	*o = RpcTrendinsightSchemasUserUserInfoResponse(varRpcTrendinsightSchemasUserUserInfoResponse)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "data")
		delete(additionalProperties, "message")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableRpcTrendinsightSchemasUserUserInfoResponse struct {
	value *RpcTrendinsightSchemasUserUserInfoResponse
	isSet bool
}

func (v NullableRpcTrendinsightSchemasUserUserInfoResponse) Get() *RpcTrendinsightSchemasUserUserInfoResponse {
	return v.value
}

func (v *NullableRpcTrendinsightSchemasUserUserInfoResponse) Set(val *RpcTrendinsightSchemasUserUserInfoResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableRpcTrendinsightSchemasUserUserInfoResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableRpcTrendinsightSchemasUserUserInfoResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRpcTrendinsightSchemasUserUserInfoResponse(val *RpcTrendinsightSchemasUserUserInfoResponse) *NullableRpcTrendinsightSchemasUserUserInfoResponse {
	return &NullableRpcTrendinsightSchemasUserUserInfoResponse{value: val, isSet: true}
}

func (v NullableRpcTrendinsightSchemasUserUserInfoResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRpcTrendinsightSchemasUserUserInfoResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


