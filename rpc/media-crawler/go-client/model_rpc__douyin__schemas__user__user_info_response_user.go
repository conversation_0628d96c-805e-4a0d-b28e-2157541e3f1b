/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"fmt"
)


// RpcDouyinSchemasUserUserInfoResponseUser 用户信息
type RpcDouyinSchemasUserUserInfoResponseUser struct {
	VideoAuthor *VideoAuthor
}

// Unmarshal JSON data into any of the pointers in the struct
func (dst *RpcDouyinSchemasUserUserInfoResponseUser) UnmarshalJSON(data []byte) error {
	var err error
	// this object is nullable so check if the payload is null or empty string
	if string(data) == "" || string(data) == "{}" {
		return nil
	}

	// try to unmarshal JSON data into VideoAuthor
	err = json.Unmarshal(data, &dst.VideoAuthor);
	if err == nil {
		jsonVideoAuthor, _ := json.Marshal(dst.VideoAuthor)
		if string(jsonVideoAuthor) == "{}" { // empty struct
			dst.VideoAuthor = nil
		} else {
			return nil // data stored in dst.VideoAuthor, return on the first match
		}
	} else {
		dst.VideoAuthor = nil
	}

	return fmt.Errorf("data failed to match schemas in anyOf(RpcDouyinSchemasUserUserInfoResponseUser)")
}

// Marshal data from the first non-nil pointers in the struct to JSON
func (src RpcDouyinSchemasUserUserInfoResponseUser) MarshalJSON() ([]byte, error) {
	if src.VideoAuthor != nil {
		return json.Marshal(&src.VideoAuthor)
	}

	return nil, nil // no data in anyOf schemas
}


type NullableRpcDouyinSchemasUserUserInfoResponseUser struct {
	value *RpcDouyinSchemasUserUserInfoResponseUser
	isSet bool
}

func (v NullableRpcDouyinSchemasUserUserInfoResponseUser) Get() *RpcDouyinSchemasUserUserInfoResponseUser {
	return v.value
}

func (v *NullableRpcDouyinSchemasUserUserInfoResponseUser) Set(val *RpcDouyinSchemasUserUserInfoResponseUser) {
	v.value = val
	v.isSet = true
}

func (v NullableRpcDouyinSchemasUserUserInfoResponseUser) IsSet() bool {
	return v.isSet
}

func (v *NullableRpcDouyinSchemasUserUserInfoResponseUser) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRpcDouyinSchemasUserUserInfoResponseUser(val *RpcDouyinSchemasUserUserInfoResponseUser) *NullableRpcDouyinSchemasUserUserInfoResponseUser {
	return &NullableRpcDouyinSchemasUserUserInfoResponseUser{value: val, isSet: true}
}

func (v NullableRpcDouyinSchemasUserUserInfoResponseUser) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRpcDouyinSchemasUserUserInfoResponseUser) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


