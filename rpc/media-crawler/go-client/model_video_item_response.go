/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the VideoItemResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &VideoItemResponse{}

// VideoItemResponse 视频项响应模型（用于同步响应中的 video_items 字段）
type VideoItemResponse struct {
	// 视频ID
	AwemeId string `json:"aweme_id"`
	// 创建时间戳
	CreateTime int64 `json:"create_time"`
}

type _VideoItemResponse VideoItemResponse

// NewVideoItemResponse instantiates a new VideoItemResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewVideoItemResponse(awemeId string, createTime int64) *VideoItemResponse {
	this := VideoItemResponse{}
	this.AwemeId = awemeId
	this.CreateTime = createTime
	return &this
}

// NewVideoItemResponseWithDefaults instantiates a new VideoItemResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewVideoItemResponseWithDefaults() *VideoItemResponse {
	this := VideoItemResponse{}
	return &this
}

// GetAwemeId returns the AwemeId field value
func (o *VideoItemResponse) GetAwemeId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.AwemeId
}

// GetAwemeIdOk returns a tuple with the AwemeId field value
// and a boolean to check if the value has been set.
func (o *VideoItemResponse) GetAwemeIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.AwemeId, true
}

// SetAwemeId sets field value
func (o *VideoItemResponse) SetAwemeId(v string) {
	o.AwemeId = v
}

// GetCreateTime returns the CreateTime field value
func (o *VideoItemResponse) GetCreateTime() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.CreateTime
}

// GetCreateTimeOk returns a tuple with the CreateTime field value
// and a boolean to check if the value has been set.
func (o *VideoItemResponse) GetCreateTimeOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CreateTime, true
}

// SetCreateTime sets field value
func (o *VideoItemResponse) SetCreateTime(v int64) {
	o.CreateTime = v
}

func (o VideoItemResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o VideoItemResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["aweme_id"] = o.AwemeId
	toSerialize["create_time"] = o.CreateTime
	return toSerialize, nil
}

func (o *VideoItemResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"aweme_id",
		"create_time",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varVideoItemResponse := _VideoItemResponse{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varVideoItemResponse)

	if err != nil {
		return err
	}

	*o = VideoItemResponse(varVideoItemResponse)

	return err
}

type NullableVideoItemResponse struct {
	value *VideoItemResponse
	isSet bool
}

func (v NullableVideoItemResponse) Get() *VideoItemResponse {
	return v.value
}

func (v *NullableVideoItemResponse) Set(val *VideoItemResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableVideoItemResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableVideoItemResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableVideoItemResponse(val *VideoItemResponse) *NullableVideoItemResponse {
	return &NullableVideoItemResponse{value: val, isSet: true}
}

func (v NullableVideoItemResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableVideoItemResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


