/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"fmt"
)

// checks if the RpcDouyinSchemasUserUserInfoResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &RpcDouyinSchemasUserUserInfoResponse{}

// RpcDouyinSchemasUserUserInfoResponse 获取用户信息响应
type RpcDouyinSchemasUserUserInfoResponse struct {
	// 状态码
	StatusCode int64 `json:"status_code"`
	User *RpcDouyinSchemasUserUserInfoResponseUser `json:"user,omitempty"`
	AdditionalProperties map[string]interface{}
}

type _RpcDouyinSchemasUserUserInfoResponse RpcDouyinSchemasUserUserInfoResponse

// NewRpcDouyinSchemasUserUserInfoResponse instantiates a new RpcDouyinSchemasUserUserInfoResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewRpcDouyinSchemasUserUserInfoResponse(statusCode int64) *RpcDouyinSchemasUserUserInfoResponse {
	this := RpcDouyinSchemasUserUserInfoResponse{}
	this.StatusCode = statusCode
	return &this
}

// NewRpcDouyinSchemasUserUserInfoResponseWithDefaults instantiates a new RpcDouyinSchemasUserUserInfoResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewRpcDouyinSchemasUserUserInfoResponseWithDefaults() *RpcDouyinSchemasUserUserInfoResponse {
	this := RpcDouyinSchemasUserUserInfoResponse{}
	return &this
}

// GetStatusCode returns the StatusCode field value
func (o *RpcDouyinSchemasUserUserInfoResponse) GetStatusCode() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.StatusCode
}

// GetStatusCodeOk returns a tuple with the StatusCode field value
// and a boolean to check if the value has been set.
func (o *RpcDouyinSchemasUserUserInfoResponse) GetStatusCodeOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.StatusCode, true
}

// SetStatusCode sets field value
func (o *RpcDouyinSchemasUserUserInfoResponse) SetStatusCode(v int64) {
	o.StatusCode = v
}

// GetUser returns the User field value if set, zero value otherwise.
func (o *RpcDouyinSchemasUserUserInfoResponse) GetUser() RpcDouyinSchemasUserUserInfoResponseUser {
	if o == nil || IsNil(o.User) {
		var ret RpcDouyinSchemasUserUserInfoResponseUser
		return ret
	}
	return *o.User
}

// GetUserOk returns a tuple with the User field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *RpcDouyinSchemasUserUserInfoResponse) GetUserOk() (*RpcDouyinSchemasUserUserInfoResponseUser, bool) {
	if o == nil || IsNil(o.User) {
		return nil, false
	}
	return o.User, true
}

// HasUser returns a boolean if a field has been set.
func (o *RpcDouyinSchemasUserUserInfoResponse) HasUser() bool {
	if o != nil && !IsNil(o.User) {
		return true
	}

	return false
}

// SetUser gets a reference to the given RpcDouyinSchemasUserUserInfoResponseUser and assigns it to the User field.
func (o *RpcDouyinSchemasUserUserInfoResponse) SetUser(v RpcDouyinSchemasUserUserInfoResponseUser) {
	o.User = &v
}

func (o RpcDouyinSchemasUserUserInfoResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o RpcDouyinSchemasUserUserInfoResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["status_code"] = o.StatusCode
	if !IsNil(o.User) {
		toSerialize["user"] = o.User
	}

	for key, value := range o.AdditionalProperties {
		toSerialize[key] = value
	}

	return toSerialize, nil
}

func (o *RpcDouyinSchemasUserUserInfoResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"status_code",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varRpcDouyinSchemasUserUserInfoResponse := _RpcDouyinSchemasUserUserInfoResponse{}

	err = json.Unmarshal(data, &varRpcDouyinSchemasUserUserInfoResponse)

	if err != nil {
		return err
	}

	*o = RpcDouyinSchemasUserUserInfoResponse(varRpcDouyinSchemasUserUserInfoResponse)

	additionalProperties := make(map[string]interface{})

	if err = json.Unmarshal(data, &additionalProperties); err == nil {
		delete(additionalProperties, "status_code")
		delete(additionalProperties, "user")
		o.AdditionalProperties = additionalProperties
	}

	return err
}

type NullableRpcDouyinSchemasUserUserInfoResponse struct {
	value *RpcDouyinSchemasUserUserInfoResponse
	isSet bool
}

func (v NullableRpcDouyinSchemasUserUserInfoResponse) Get() *RpcDouyinSchemasUserUserInfoResponse {
	return v.value
}

func (v *NullableRpcDouyinSchemasUserUserInfoResponse) Set(val *RpcDouyinSchemasUserUserInfoResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableRpcDouyinSchemasUserUserInfoResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableRpcDouyinSchemasUserUserInfoResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRpcDouyinSchemasUserUserInfoResponse(val *RpcDouyinSchemasUserUserInfoResponse) *NullableRpcDouyinSchemasUserUserInfoResponse {
	return &NullableRpcDouyinSchemasUserUserInfoResponse{value: val, isSet: true}
}

func (v NullableRpcDouyinSchemasUserUserInfoResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRpcDouyinSchemasUserUserInfoResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


