/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the VideoProcessResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &VideoProcessResponse{}

// VideoProcessResponse 视频处理响应模型（用于 process/{video_id} 端点）
type VideoProcessResponse struct {
	// 视频ID
	VideoId string `json:"video_id"`
	// 输入类型
	InputType string `json:"input_type"`
	// 原始输入
	OriginalInput string `json:"original_input"`
	// 是否处理成功
	Processed bool `json:"processed"`
	// 视频数据
	Data DouyinAwemeResponse `json:"data"`
	// 数据来源
	Source string `json:"source"`
}

type _VideoProcessResponse VideoProcessResponse

// NewVideoProcessResponse instantiates a new VideoProcessResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewVideoProcessResponse(videoId string, inputType string, originalInput string, processed bool, data DouyinAwemeResponse, source string) *VideoProcessResponse {
	this := VideoProcessResponse{}
	this.VideoId = videoId
	this.InputType = inputType
	this.OriginalInput = originalInput
	this.Processed = processed
	this.Data = data
	this.Source = source
	return &this
}

// NewVideoProcessResponseWithDefaults instantiates a new VideoProcessResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewVideoProcessResponseWithDefaults() *VideoProcessResponse {
	this := VideoProcessResponse{}
	return &this
}

// GetVideoId returns the VideoId field value
func (o *VideoProcessResponse) GetVideoId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.VideoId
}

// GetVideoIdOk returns a tuple with the VideoId field value
// and a boolean to check if the value has been set.
func (o *VideoProcessResponse) GetVideoIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.VideoId, true
}

// SetVideoId sets field value
func (o *VideoProcessResponse) SetVideoId(v string) {
	o.VideoId = v
}

// GetInputType returns the InputType field value
func (o *VideoProcessResponse) GetInputType() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.InputType
}

// GetInputTypeOk returns a tuple with the InputType field value
// and a boolean to check if the value has been set.
func (o *VideoProcessResponse) GetInputTypeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.InputType, true
}

// SetInputType sets field value
func (o *VideoProcessResponse) SetInputType(v string) {
	o.InputType = v
}

// GetOriginalInput returns the OriginalInput field value
func (o *VideoProcessResponse) GetOriginalInput() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.OriginalInput
}

// GetOriginalInputOk returns a tuple with the OriginalInput field value
// and a boolean to check if the value has been set.
func (o *VideoProcessResponse) GetOriginalInputOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.OriginalInput, true
}

// SetOriginalInput sets field value
func (o *VideoProcessResponse) SetOriginalInput(v string) {
	o.OriginalInput = v
}

// GetProcessed returns the Processed field value
func (o *VideoProcessResponse) GetProcessed() bool {
	if o == nil {
		var ret bool
		return ret
	}

	return o.Processed
}

// GetProcessedOk returns a tuple with the Processed field value
// and a boolean to check if the value has been set.
func (o *VideoProcessResponse) GetProcessedOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Processed, true
}

// SetProcessed sets field value
func (o *VideoProcessResponse) SetProcessed(v bool) {
	o.Processed = v
}

// GetData returns the Data field value
func (o *VideoProcessResponse) GetData() DouyinAwemeResponse {
	if o == nil {
		var ret DouyinAwemeResponse
		return ret
	}

	return o.Data
}

// GetDataOk returns a tuple with the Data field value
// and a boolean to check if the value has been set.
func (o *VideoProcessResponse) GetDataOk() (*DouyinAwemeResponse, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Data, true
}

// SetData sets field value
func (o *VideoProcessResponse) SetData(v DouyinAwemeResponse) {
	o.Data = v
}

// GetSource returns the Source field value
func (o *VideoProcessResponse) GetSource() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Source
}

// GetSourceOk returns a tuple with the Source field value
// and a boolean to check if the value has been set.
func (o *VideoProcessResponse) GetSourceOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Source, true
}

// SetSource sets field value
func (o *VideoProcessResponse) SetSource(v string) {
	o.Source = v
}

func (o VideoProcessResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o VideoProcessResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["video_id"] = o.VideoId
	toSerialize["input_type"] = o.InputType
	toSerialize["original_input"] = o.OriginalInput
	toSerialize["processed"] = o.Processed
	toSerialize["data"] = o.Data
	toSerialize["source"] = o.Source
	return toSerialize, nil
}

func (o *VideoProcessResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"video_id",
		"input_type",
		"original_input",
		"processed",
		"data",
		"source",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varVideoProcessResponse := _VideoProcessResponse{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varVideoProcessResponse)

	if err != nil {
		return err
	}

	*o = VideoProcessResponse(varVideoProcessResponse)

	return err
}

type NullableVideoProcessResponse struct {
	value *VideoProcessResponse
	isSet bool
}

func (v NullableVideoProcessResponse) Get() *VideoProcessResponse {
	return v.value
}

func (v *NullableVideoProcessResponse) Set(val *VideoProcessResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableVideoProcessResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableVideoProcessResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableVideoProcessResponse(val *VideoProcessResponse) *NullableVideoProcessResponse {
	return &NullableVideoProcessResponse{value: val, isSet: true}
}

func (v NullableVideoProcessResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableVideoProcessResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


