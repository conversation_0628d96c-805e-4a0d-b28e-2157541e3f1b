# VideoTrendData

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwemeId** | **string** | 视频ID | 
**TrendScore** | **float32** | 趋势评分 | 
**CreatedAt** | **string** | 创建时间 | 
**UpdatedAt** | **string** | 更新时间 | 
**IsDeleted** | Pointer to **bool** | 是否已删除 | [optional] [default to false]

## Methods

### NewVideoTrendData

`func NewVideoTrendData(awemeId string, trendScore float32, createdAt string, updatedAt string, ) *VideoTrendData`

NewVideoTrendData instantiates a new VideoTrendData object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewVideoTrendDataWithDefaults

`func NewVideoTrendDataWithDefaults() *VideoTrendData`

NewVideoTrendDataWithDefaults instantiates a new VideoTrendData object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetAwemeId

`func (o *VideoTrendData) GetAwemeId() string`

GetAwemeId returns the AwemeId field if non-nil, zero value otherwise.

### GetAwemeIdOk

`func (o *VideoTrendData) GetAwemeIdOk() (*string, bool)`

GetAwemeIdOk returns a tuple with the AwemeId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAwemeId

`func (o *VideoTrendData) SetAwemeId(v string)`

SetAwemeId sets AwemeId field to given value.


### GetTrendScore

`func (o *VideoTrendData) GetTrendScore() float32`

GetTrendScore returns the TrendScore field if non-nil, zero value otherwise.

### GetTrendScoreOk

`func (o *VideoTrendData) GetTrendScoreOk() (*float32, bool)`

GetTrendScoreOk returns a tuple with the TrendScore field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTrendScore

`func (o *VideoTrendData) SetTrendScore(v float32)`

SetTrendScore sets TrendScore field to given value.


### GetCreatedAt

`func (o *VideoTrendData) GetCreatedAt() string`

GetCreatedAt returns the CreatedAt field if non-nil, zero value otherwise.

### GetCreatedAtOk

`func (o *VideoTrendData) GetCreatedAtOk() (*string, bool)`

GetCreatedAtOk returns a tuple with the CreatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedAt

`func (o *VideoTrendData) SetCreatedAt(v string)`

SetCreatedAt sets CreatedAt field to given value.


### GetUpdatedAt

`func (o *VideoTrendData) GetUpdatedAt() string`

GetUpdatedAt returns the UpdatedAt field if non-nil, zero value otherwise.

### GetUpdatedAtOk

`func (o *VideoTrendData) GetUpdatedAtOk() (*string, bool)`

GetUpdatedAtOk returns a tuple with the UpdatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdatedAt

`func (o *VideoTrendData) SetUpdatedAt(v string)`

SetUpdatedAt sets UpdatedAt field to given value.


### GetIsDeleted

`func (o *VideoTrendData) GetIsDeleted() bool`

GetIsDeleted returns the IsDeleted field if non-nil, zero value otherwise.

### GetIsDeletedOk

`func (o *VideoTrendData) GetIsDeletedOk() (*bool, bool)`

GetIsDeletedOk returns a tuple with the IsDeleted field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIsDeleted

`func (o *VideoTrendData) SetIsDeleted(v bool)`

SetIsDeleted sets IsDeleted field to given value.

### HasIsDeleted

`func (o *VideoTrendData) HasIsDeleted() bool`

HasIsDeleted returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


