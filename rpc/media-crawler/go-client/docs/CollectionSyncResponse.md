# CollectionSyncResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CollectionsSynced** | **int64** | 同步的收藏夹数量 | 
**VideosSynced** | **int64** | 同步的视频数量 | 
**CollectionsFiltered** | **int64** | 过滤后的收藏夹数量 | 
**RelationsCreated** | **int64** | 创建的关联关系数量 | 
**RelationsExisting** | **int64** | 已存在的关联关系数量 | 
**TrendinsightRelationsCreated** | **int64** | 创建的TrendInsight视频关联数量 | 
**TrendinsightRelationsExisting** | **int64** | 已存在的TrendInsight视频关联数量 | 
**AwemeIds** | **[]string** | 获取到的所有视频aweme_id列表 | 
**VideoItems** | Pointer to [**[]VideoItemResponse**](VideoItemResponse.md) | 视频详细信息列表 | [optional] 
**Errors** | Pointer to **[]interface{}** | 错误信息列表 | [optional] 

## Methods

### NewCollectionSyncResponse

`func NewCollectionSyncResponse(collectionsSynced int64, videosSynced int64, collectionsFiltered int64, relationsCreated int64, relationsExisting int64, trendinsightRelationsCreated int64, trendinsightRelationsExisting int64, awemeIds []string, ) *CollectionSyncResponse`

NewCollectionSyncResponse instantiates a new CollectionSyncResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewCollectionSyncResponseWithDefaults

`func NewCollectionSyncResponseWithDefaults() *CollectionSyncResponse`

NewCollectionSyncResponseWithDefaults instantiates a new CollectionSyncResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCollectionsSynced

`func (o *CollectionSyncResponse) GetCollectionsSynced() int64`

GetCollectionsSynced returns the CollectionsSynced field if non-nil, zero value otherwise.

### GetCollectionsSyncedOk

`func (o *CollectionSyncResponse) GetCollectionsSyncedOk() (*int64, bool)`

GetCollectionsSyncedOk returns a tuple with the CollectionsSynced field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCollectionsSynced

`func (o *CollectionSyncResponse) SetCollectionsSynced(v int64)`

SetCollectionsSynced sets CollectionsSynced field to given value.


### GetVideosSynced

`func (o *CollectionSyncResponse) GetVideosSynced() int64`

GetVideosSynced returns the VideosSynced field if non-nil, zero value otherwise.

### GetVideosSyncedOk

`func (o *CollectionSyncResponse) GetVideosSyncedOk() (*int64, bool)`

GetVideosSyncedOk returns a tuple with the VideosSynced field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetVideosSynced

`func (o *CollectionSyncResponse) SetVideosSynced(v int64)`

SetVideosSynced sets VideosSynced field to given value.


### GetCollectionsFiltered

`func (o *CollectionSyncResponse) GetCollectionsFiltered() int64`

GetCollectionsFiltered returns the CollectionsFiltered field if non-nil, zero value otherwise.

### GetCollectionsFilteredOk

`func (o *CollectionSyncResponse) GetCollectionsFilteredOk() (*int64, bool)`

GetCollectionsFilteredOk returns a tuple with the CollectionsFiltered field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCollectionsFiltered

`func (o *CollectionSyncResponse) SetCollectionsFiltered(v int64)`

SetCollectionsFiltered sets CollectionsFiltered field to given value.


### GetRelationsCreated

`func (o *CollectionSyncResponse) GetRelationsCreated() int64`

GetRelationsCreated returns the RelationsCreated field if non-nil, zero value otherwise.

### GetRelationsCreatedOk

`func (o *CollectionSyncResponse) GetRelationsCreatedOk() (*int64, bool)`

GetRelationsCreatedOk returns a tuple with the RelationsCreated field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRelationsCreated

`func (o *CollectionSyncResponse) SetRelationsCreated(v int64)`

SetRelationsCreated sets RelationsCreated field to given value.


### GetRelationsExisting

`func (o *CollectionSyncResponse) GetRelationsExisting() int64`

GetRelationsExisting returns the RelationsExisting field if non-nil, zero value otherwise.

### GetRelationsExistingOk

`func (o *CollectionSyncResponse) GetRelationsExistingOk() (*int64, bool)`

GetRelationsExistingOk returns a tuple with the RelationsExisting field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRelationsExisting

`func (o *CollectionSyncResponse) SetRelationsExisting(v int64)`

SetRelationsExisting sets RelationsExisting field to given value.


### GetTrendinsightRelationsCreated

`func (o *CollectionSyncResponse) GetTrendinsightRelationsCreated() int64`

GetTrendinsightRelationsCreated returns the TrendinsightRelationsCreated field if non-nil, zero value otherwise.

### GetTrendinsightRelationsCreatedOk

`func (o *CollectionSyncResponse) GetTrendinsightRelationsCreatedOk() (*int64, bool)`

GetTrendinsightRelationsCreatedOk returns a tuple with the TrendinsightRelationsCreated field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTrendinsightRelationsCreated

`func (o *CollectionSyncResponse) SetTrendinsightRelationsCreated(v int64)`

SetTrendinsightRelationsCreated sets TrendinsightRelationsCreated field to given value.


### GetTrendinsightRelationsExisting

`func (o *CollectionSyncResponse) GetTrendinsightRelationsExisting() int64`

GetTrendinsightRelationsExisting returns the TrendinsightRelationsExisting field if non-nil, zero value otherwise.

### GetTrendinsightRelationsExistingOk

`func (o *CollectionSyncResponse) GetTrendinsightRelationsExistingOk() (*int64, bool)`

GetTrendinsightRelationsExistingOk returns a tuple with the TrendinsightRelationsExisting field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTrendinsightRelationsExisting

`func (o *CollectionSyncResponse) SetTrendinsightRelationsExisting(v int64)`

SetTrendinsightRelationsExisting sets TrendinsightRelationsExisting field to given value.


### GetAwemeIds

`func (o *CollectionSyncResponse) GetAwemeIds() []string`

GetAwemeIds returns the AwemeIds field if non-nil, zero value otherwise.

### GetAwemeIdsOk

`func (o *CollectionSyncResponse) GetAwemeIdsOk() (*[]string, bool)`

GetAwemeIdsOk returns a tuple with the AwemeIds field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAwemeIds

`func (o *CollectionSyncResponse) SetAwemeIds(v []string)`

SetAwemeIds sets AwemeIds field to given value.


### GetVideoItems

`func (o *CollectionSyncResponse) GetVideoItems() []VideoItemResponse`

GetVideoItems returns the VideoItems field if non-nil, zero value otherwise.

### GetVideoItemsOk

`func (o *CollectionSyncResponse) GetVideoItemsOk() (*[]VideoItemResponse, bool)`

GetVideoItemsOk returns a tuple with the VideoItems field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetVideoItems

`func (o *CollectionSyncResponse) SetVideoItems(v []VideoItemResponse)`

SetVideoItems sets VideoItems field to given value.

### HasVideoItems

`func (o *CollectionSyncResponse) HasVideoItems() bool`

HasVideoItems returns a boolean if a field has been set.

### GetErrors

`func (o *CollectionSyncResponse) GetErrors() []interface{}`

GetErrors returns the Errors field if non-nil, zero value otherwise.

### GetErrorsOk

`func (o *CollectionSyncResponse) GetErrorsOk() (*[]interface{}, bool)`

GetErrorsOk returns a tuple with the Errors field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetErrors

`func (o *CollectionSyncResponse) SetErrors(v []interface{})`

SetErrors sets Errors field to given value.

### HasErrors

`func (o *CollectionSyncResponse) HasErrors() bool`

HasErrors returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


