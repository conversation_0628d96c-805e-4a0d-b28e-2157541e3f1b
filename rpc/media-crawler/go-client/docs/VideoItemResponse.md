# VideoItemResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AwemeId** | **string** | 视频ID | 
**CreateTime** | **int64** | 创建时间戳 | 

## Methods

### NewVideoItemResponse

`func NewVideoItemResponse(awemeId string, createTime int64, ) *VideoItemResponse`

NewVideoItemResponse instantiates a new VideoItemResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewVideoItemResponseWithDefaults

`func NewVideoItemResponseWithDefaults() *VideoItemResponse`

NewVideoItemResponseWithDefaults instantiates a new VideoItemResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetAwemeId

`func (o *VideoItemResponse) GetAwemeId() string`

GetAwemeId returns the AwemeId field if non-nil, zero value otherwise.

### GetAwemeIdOk

`func (o *VideoItemResponse) GetAwemeIdOk() (*string, bool)`

GetAwemeIdOk returns a tuple with the AwemeId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAwemeId

`func (o *VideoItemResponse) SetAwemeId(v string)`

SetAwemeId sets AwemeId field to given value.


### GetCreateTime

`func (o *VideoItemResponse) GetCreateTime() int64`

GetCreateTime returns the CreateTime field if non-nil, zero value otherwise.

### GetCreateTimeOk

`func (o *VideoItemResponse) GetCreateTimeOk() (*int64, bool)`

GetCreateTimeOk returns a tuple with the CreateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreateTime

`func (o *VideoItemResponse) SetCreateTime(v int64)`

SetCreateTime sets CreateTime field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


