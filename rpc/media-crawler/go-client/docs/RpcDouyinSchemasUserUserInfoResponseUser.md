# RpcDouyinSchemasUserUserInfoResponseUser

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Uid** | Pointer to **string** | 用户ID | [optional] [default to ""]
**Nickname** | Pointer to **string** | 昵称 | [optional] [default to ""]
**SecUid** | Pointer to **string** | 安全用户ID | [optional] [default to ""]
**UniqueId** | Pointer to **string** | 用户唯一ID | [optional] [default to ""]

## Methods

### NewRpcDouyinSchemasUserUserInfoResponseUser

`func NewRpcDouyinSchemasUserUserInfoResponseUser() *RpcDouyinSchemasUserUserInfoResponseUser`

NewRpcDouyinSchemasUserUserInfoResponseUser instantiates a new RpcDouyinSchemasUserUserInfoResponseUser object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewRpcDouyinSchemasUserUserInfoResponseUserWithDefaults

`func NewRpcDouyinSchemasUserUserInfoResponseUserWithDefaults() *RpcDouyinSchemasUserUserInfoResponseUser`

NewRpcDouyinSchemasUserUserInfoResponseUserWithDefaults instantiates a new RpcDouyinSchemasUserUserInfoResponseUser object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetUid

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) GetUid() string`

GetUid returns the Uid field if non-nil, zero value otherwise.

### GetUidOk

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) GetUidOk() (*string, bool)`

GetUidOk returns a tuple with the Uid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUid

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) SetUid(v string)`

SetUid sets Uid field to given value.

### HasUid

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) HasUid() bool`

HasUid returns a boolean if a field has been set.

### GetNickname

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) GetNickname() string`

GetNickname returns the Nickname field if non-nil, zero value otherwise.

### GetNicknameOk

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) GetNicknameOk() (*string, bool)`

GetNicknameOk returns a tuple with the Nickname field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNickname

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) SetNickname(v string)`

SetNickname sets Nickname field to given value.

### HasNickname

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) HasNickname() bool`

HasNickname returns a boolean if a field has been set.

### GetSecUid

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) GetSecUid() string`

GetSecUid returns the SecUid field if non-nil, zero value otherwise.

### GetSecUidOk

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) GetSecUidOk() (*string, bool)`

GetSecUidOk returns a tuple with the SecUid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSecUid

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) SetSecUid(v string)`

SetSecUid sets SecUid field to given value.

### HasSecUid

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) HasSecUid() bool`

HasSecUid returns a boolean if a field has been set.

### GetUniqueId

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) GetUniqueId() string`

GetUniqueId returns the UniqueId field if non-nil, zero value otherwise.

### GetUniqueIdOk

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) GetUniqueIdOk() (*string, bool)`

GetUniqueIdOk returns a tuple with the UniqueId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUniqueId

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) SetUniqueId(v string)`

SetUniqueId sets UniqueId field to given value.

### HasUniqueId

`func (o *RpcDouyinSchemasUserUserInfoResponseUser) HasUniqueId() bool`

HasUniqueId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


