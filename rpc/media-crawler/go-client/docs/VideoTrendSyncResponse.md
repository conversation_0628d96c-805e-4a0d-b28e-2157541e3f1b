# VideoTrendSyncResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Success** | **bool** | 操作是否成功 | 
**Message** | **string** | 操作结果消息 | 
**Data** | Pointer to [**NullableVideoTrendData**](VideoTrendData.md) |  | [optional] 
**Error** | Pointer to **NullableString** |  | [optional] 

## Methods

### NewVideoTrendSyncResponse

`func NewVideoTrendSyncResponse(success bool, message string, ) *VideoTrendSyncResponse`

NewVideoTrendSyncResponse instantiates a new VideoTrendSyncResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewVideoTrendSyncResponseWithDefaults

`func NewVideoTrendSyncResponseWithDefaults() *VideoTrendSyncResponse`

NewVideoTrendSyncResponseWithDefaults instantiates a new VideoTrendSyncResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetSuccess

`func (o *VideoTrendSyncResponse) GetSuccess() bool`

GetSuccess returns the Success field if non-nil, zero value otherwise.

### GetSuccessOk

`func (o *VideoTrendSyncResponse) GetSuccessOk() (*bool, bool)`

GetSuccessOk returns a tuple with the Success field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSuccess

`func (o *VideoTrendSyncResponse) SetSuccess(v bool)`

SetSuccess sets Success field to given value.


### GetMessage

`func (o *VideoTrendSyncResponse) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *VideoTrendSyncResponse) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *VideoTrendSyncResponse) SetMessage(v string)`

SetMessage sets Message field to given value.


### GetData

`func (o *VideoTrendSyncResponse) GetData() VideoTrendData`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *VideoTrendSyncResponse) GetDataOk() (*VideoTrendData, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *VideoTrendSyncResponse) SetData(v VideoTrendData)`

SetData sets Data field to given value.

### HasData

`func (o *VideoTrendSyncResponse) HasData() bool`

HasData returns a boolean if a field has been set.

### SetDataNil

`func (o *VideoTrendSyncResponse) SetDataNil(b bool)`

 SetDataNil sets the value for Data to be an explicit nil

### UnsetData
`func (o *VideoTrendSyncResponse) UnsetData()`

UnsetData ensures that no value is present for Data, not even an explicit nil
### GetError

`func (o *VideoTrendSyncResponse) GetError() string`

GetError returns the Error field if non-nil, zero value otherwise.

### GetErrorOk

`func (o *VideoTrendSyncResponse) GetErrorOk() (*string, bool)`

GetErrorOk returns a tuple with the Error field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetError

`func (o *VideoTrendSyncResponse) SetError(v string)`

SetError sets Error field to given value.

### HasError

`func (o *VideoTrendSyncResponse) HasError() bool`

HasError returns a boolean if a field has been set.

### SetErrorNil

`func (o *VideoTrendSyncResponse) SetErrorNil(b bool)`

 SetErrorNil sets the value for Error to be an explicit nil

### UnsetError
`func (o *VideoTrendSyncResponse) UnsetError()`

UnsetError ensures that no value is present for Error, not even an explicit nil

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


