# RpcDouyinSchemasUserUserInfoResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**StatusCode** | **int64** | 状态码 | 
**User** | Pointer to [**RpcDouyinSchemasUserUserInfoResponseUser**](RpcDouyinSchemasUserUserInfoResponseUser.md) |  | [optional] 

## Methods

### NewRpcDouyinSchemasUserUserInfoResponse

`func NewRpcDouyinSchemasUserUserInfoResponse(statusCode int64, ) *RpcDouyinSchemasUserUserInfoResponse`

NewRpcDouyinSchemasUserUserInfoResponse instantiates a new RpcDouyinSchemasUserUserInfoResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewRpcDouyinSchemasUserUserInfoResponseWithDefaults

`func NewRpcDouyinSchemasUserUserInfoResponseWithDefaults() *RpcDouyinSchemasUserUserInfoResponse`

NewRpcDouyinSchemasUserUserInfoResponseWithDefaults instantiates a new RpcDouyinSchemasUserUserInfoResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetStatusCode

`func (o *RpcDouyinSchemasUserUserInfoResponse) GetStatusCode() int64`

GetStatusCode returns the StatusCode field if non-nil, zero value otherwise.

### GetStatusCodeOk

`func (o *RpcDouyinSchemasUserUserInfoResponse) GetStatusCodeOk() (*int64, bool)`

GetStatusCodeOk returns a tuple with the StatusCode field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStatusCode

`func (o *RpcDouyinSchemasUserUserInfoResponse) SetStatusCode(v int64)`

SetStatusCode sets StatusCode field to given value.


### GetUser

`func (o *RpcDouyinSchemasUserUserInfoResponse) GetUser() RpcDouyinSchemasUserUserInfoResponseUser`

GetUser returns the User field if non-nil, zero value otherwise.

### GetUserOk

`func (o *RpcDouyinSchemasUserUserInfoResponse) GetUserOk() (*RpcDouyinSchemasUserUserInfoResponseUser, bool)`

GetUserOk returns a tuple with the User field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUser

`func (o *RpcDouyinSchemasUserUserInfoResponse) SetUser(v RpcDouyinSchemasUserUserInfoResponseUser)`

SetUser sets User field to given value.

### HasUser

`func (o *RpcDouyinSchemasUserUserInfoResponse) HasUser() bool`

HasUser returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


