# DouyinAwemeResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **NullableInt64** |  | [optional] 
**UserId** | Pointer to **NullableString** |  | [optional] 
**SecUid** | Pointer to **NullableString** |  | [optional] 
**ShortUserId** | Pointer to **NullableString** |  | [optional] 
**UserUniqueId** | Pointer to **NullableString** |  | [optional] 
**Nickname** | Pointer to **NullableString** |  | [optional] 
**Avatar** | Pointer to **NullableString** |  | [optional] 
**UserSignature** | Pointer to **NullableString** |  | [optional] 
**IpLocation** | Pointer to **NullableString** |  | [optional] 
**AwemeId** | **string** | 视频ID | 
**AwemeType** | Pointer to **NullableString** |  | [optional] 
**Title** | Pointer to **NullableString** |  | [optional] 
**Desc** | Pointer to **NullableString** |  | [optional] 
**CreateTime** | Pointer to **NullableString** |  | [optional] 
**LikedCount** | Pointer to **NullableString** |  | [optional] 
**CommentCount** | Pointer to **NullableString** |  | [optional] 
**ShareCount** | Pointer to **NullableString** |  | [optional] 
**CollectedCount** | Pointer to **NullableString** |  | [optional] 
**AwemeUrl** | Pointer to **NullableString** |  | [optional] 
**CoverUrl** | Pointer to **NullableString** |  | [optional] 
**VideoDownloadUrl** | Pointer to **NullableString** |  | [optional] 
**SourceKeyword** | Pointer to **NullableString** |  | [optional] 

## Methods

### NewDouyinAwemeResponse

`func NewDouyinAwemeResponse(awemeId string, ) *DouyinAwemeResponse`

NewDouyinAwemeResponse instantiates a new DouyinAwemeResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDouyinAwemeResponseWithDefaults

`func NewDouyinAwemeResponseWithDefaults() *DouyinAwemeResponse`

NewDouyinAwemeResponseWithDefaults instantiates a new DouyinAwemeResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *DouyinAwemeResponse) GetId() int64`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *DouyinAwemeResponse) GetIdOk() (*int64, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *DouyinAwemeResponse) SetId(v int64)`

SetId sets Id field to given value.

### HasId

`func (o *DouyinAwemeResponse) HasId() bool`

HasId returns a boolean if a field has been set.

### SetIdNil

`func (o *DouyinAwemeResponse) SetIdNil(b bool)`

 SetIdNil sets the value for Id to be an explicit nil

### UnsetId
`func (o *DouyinAwemeResponse) UnsetId()`

UnsetId ensures that no value is present for Id, not even an explicit nil
### GetUserId

`func (o *DouyinAwemeResponse) GetUserId() string`

GetUserId returns the UserId field if non-nil, zero value otherwise.

### GetUserIdOk

`func (o *DouyinAwemeResponse) GetUserIdOk() (*string, bool)`

GetUserIdOk returns a tuple with the UserId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUserId

`func (o *DouyinAwemeResponse) SetUserId(v string)`

SetUserId sets UserId field to given value.

### HasUserId

`func (o *DouyinAwemeResponse) HasUserId() bool`

HasUserId returns a boolean if a field has been set.

### SetUserIdNil

`func (o *DouyinAwemeResponse) SetUserIdNil(b bool)`

 SetUserIdNil sets the value for UserId to be an explicit nil

### UnsetUserId
`func (o *DouyinAwemeResponse) UnsetUserId()`

UnsetUserId ensures that no value is present for UserId, not even an explicit nil
### GetSecUid

`func (o *DouyinAwemeResponse) GetSecUid() string`

GetSecUid returns the SecUid field if non-nil, zero value otherwise.

### GetSecUidOk

`func (o *DouyinAwemeResponse) GetSecUidOk() (*string, bool)`

GetSecUidOk returns a tuple with the SecUid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSecUid

`func (o *DouyinAwemeResponse) SetSecUid(v string)`

SetSecUid sets SecUid field to given value.

### HasSecUid

`func (o *DouyinAwemeResponse) HasSecUid() bool`

HasSecUid returns a boolean if a field has been set.

### SetSecUidNil

`func (o *DouyinAwemeResponse) SetSecUidNil(b bool)`

 SetSecUidNil sets the value for SecUid to be an explicit nil

### UnsetSecUid
`func (o *DouyinAwemeResponse) UnsetSecUid()`

UnsetSecUid ensures that no value is present for SecUid, not even an explicit nil
### GetShortUserId

`func (o *DouyinAwemeResponse) GetShortUserId() string`

GetShortUserId returns the ShortUserId field if non-nil, zero value otherwise.

### GetShortUserIdOk

`func (o *DouyinAwemeResponse) GetShortUserIdOk() (*string, bool)`

GetShortUserIdOk returns a tuple with the ShortUserId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetShortUserId

`func (o *DouyinAwemeResponse) SetShortUserId(v string)`

SetShortUserId sets ShortUserId field to given value.

### HasShortUserId

`func (o *DouyinAwemeResponse) HasShortUserId() bool`

HasShortUserId returns a boolean if a field has been set.

### SetShortUserIdNil

`func (o *DouyinAwemeResponse) SetShortUserIdNil(b bool)`

 SetShortUserIdNil sets the value for ShortUserId to be an explicit nil

### UnsetShortUserId
`func (o *DouyinAwemeResponse) UnsetShortUserId()`

UnsetShortUserId ensures that no value is present for ShortUserId, not even an explicit nil
### GetUserUniqueId

`func (o *DouyinAwemeResponse) GetUserUniqueId() string`

GetUserUniqueId returns the UserUniqueId field if non-nil, zero value otherwise.

### GetUserUniqueIdOk

`func (o *DouyinAwemeResponse) GetUserUniqueIdOk() (*string, bool)`

GetUserUniqueIdOk returns a tuple with the UserUniqueId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUserUniqueId

`func (o *DouyinAwemeResponse) SetUserUniqueId(v string)`

SetUserUniqueId sets UserUniqueId field to given value.

### HasUserUniqueId

`func (o *DouyinAwemeResponse) HasUserUniqueId() bool`

HasUserUniqueId returns a boolean if a field has been set.

### SetUserUniqueIdNil

`func (o *DouyinAwemeResponse) SetUserUniqueIdNil(b bool)`

 SetUserUniqueIdNil sets the value for UserUniqueId to be an explicit nil

### UnsetUserUniqueId
`func (o *DouyinAwemeResponse) UnsetUserUniqueId()`

UnsetUserUniqueId ensures that no value is present for UserUniqueId, not even an explicit nil
### GetNickname

`func (o *DouyinAwemeResponse) GetNickname() string`

GetNickname returns the Nickname field if non-nil, zero value otherwise.

### GetNicknameOk

`func (o *DouyinAwemeResponse) GetNicknameOk() (*string, bool)`

GetNicknameOk returns a tuple with the Nickname field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNickname

`func (o *DouyinAwemeResponse) SetNickname(v string)`

SetNickname sets Nickname field to given value.

### HasNickname

`func (o *DouyinAwemeResponse) HasNickname() bool`

HasNickname returns a boolean if a field has been set.

### SetNicknameNil

`func (o *DouyinAwemeResponse) SetNicknameNil(b bool)`

 SetNicknameNil sets the value for Nickname to be an explicit nil

### UnsetNickname
`func (o *DouyinAwemeResponse) UnsetNickname()`

UnsetNickname ensures that no value is present for Nickname, not even an explicit nil
### GetAvatar

`func (o *DouyinAwemeResponse) GetAvatar() string`

GetAvatar returns the Avatar field if non-nil, zero value otherwise.

### GetAvatarOk

`func (o *DouyinAwemeResponse) GetAvatarOk() (*string, bool)`

GetAvatarOk returns a tuple with the Avatar field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAvatar

`func (o *DouyinAwemeResponse) SetAvatar(v string)`

SetAvatar sets Avatar field to given value.

### HasAvatar

`func (o *DouyinAwemeResponse) HasAvatar() bool`

HasAvatar returns a boolean if a field has been set.

### SetAvatarNil

`func (o *DouyinAwemeResponse) SetAvatarNil(b bool)`

 SetAvatarNil sets the value for Avatar to be an explicit nil

### UnsetAvatar
`func (o *DouyinAwemeResponse) UnsetAvatar()`

UnsetAvatar ensures that no value is present for Avatar, not even an explicit nil
### GetUserSignature

`func (o *DouyinAwemeResponse) GetUserSignature() string`

GetUserSignature returns the UserSignature field if non-nil, zero value otherwise.

### GetUserSignatureOk

`func (o *DouyinAwemeResponse) GetUserSignatureOk() (*string, bool)`

GetUserSignatureOk returns a tuple with the UserSignature field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUserSignature

`func (o *DouyinAwemeResponse) SetUserSignature(v string)`

SetUserSignature sets UserSignature field to given value.

### HasUserSignature

`func (o *DouyinAwemeResponse) HasUserSignature() bool`

HasUserSignature returns a boolean if a field has been set.

### SetUserSignatureNil

`func (o *DouyinAwemeResponse) SetUserSignatureNil(b bool)`

 SetUserSignatureNil sets the value for UserSignature to be an explicit nil

### UnsetUserSignature
`func (o *DouyinAwemeResponse) UnsetUserSignature()`

UnsetUserSignature ensures that no value is present for UserSignature, not even an explicit nil
### GetIpLocation

`func (o *DouyinAwemeResponse) GetIpLocation() string`

GetIpLocation returns the IpLocation field if non-nil, zero value otherwise.

### GetIpLocationOk

`func (o *DouyinAwemeResponse) GetIpLocationOk() (*string, bool)`

GetIpLocationOk returns a tuple with the IpLocation field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIpLocation

`func (o *DouyinAwemeResponse) SetIpLocation(v string)`

SetIpLocation sets IpLocation field to given value.

### HasIpLocation

`func (o *DouyinAwemeResponse) HasIpLocation() bool`

HasIpLocation returns a boolean if a field has been set.

### SetIpLocationNil

`func (o *DouyinAwemeResponse) SetIpLocationNil(b bool)`

 SetIpLocationNil sets the value for IpLocation to be an explicit nil

### UnsetIpLocation
`func (o *DouyinAwemeResponse) UnsetIpLocation()`

UnsetIpLocation ensures that no value is present for IpLocation, not even an explicit nil
### GetAwemeId

`func (o *DouyinAwemeResponse) GetAwemeId() string`

GetAwemeId returns the AwemeId field if non-nil, zero value otherwise.

### GetAwemeIdOk

`func (o *DouyinAwemeResponse) GetAwemeIdOk() (*string, bool)`

GetAwemeIdOk returns a tuple with the AwemeId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAwemeId

`func (o *DouyinAwemeResponse) SetAwemeId(v string)`

SetAwemeId sets AwemeId field to given value.


### GetAwemeType

`func (o *DouyinAwemeResponse) GetAwemeType() string`

GetAwemeType returns the AwemeType field if non-nil, zero value otherwise.

### GetAwemeTypeOk

`func (o *DouyinAwemeResponse) GetAwemeTypeOk() (*string, bool)`

GetAwemeTypeOk returns a tuple with the AwemeType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAwemeType

`func (o *DouyinAwemeResponse) SetAwemeType(v string)`

SetAwemeType sets AwemeType field to given value.

### HasAwemeType

`func (o *DouyinAwemeResponse) HasAwemeType() bool`

HasAwemeType returns a boolean if a field has been set.

### SetAwemeTypeNil

`func (o *DouyinAwemeResponse) SetAwemeTypeNil(b bool)`

 SetAwemeTypeNil sets the value for AwemeType to be an explicit nil

### UnsetAwemeType
`func (o *DouyinAwemeResponse) UnsetAwemeType()`

UnsetAwemeType ensures that no value is present for AwemeType, not even an explicit nil
### GetTitle

`func (o *DouyinAwemeResponse) GetTitle() string`

GetTitle returns the Title field if non-nil, zero value otherwise.

### GetTitleOk

`func (o *DouyinAwemeResponse) GetTitleOk() (*string, bool)`

GetTitleOk returns a tuple with the Title field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTitle

`func (o *DouyinAwemeResponse) SetTitle(v string)`

SetTitle sets Title field to given value.

### HasTitle

`func (o *DouyinAwemeResponse) HasTitle() bool`

HasTitle returns a boolean if a field has been set.

### SetTitleNil

`func (o *DouyinAwemeResponse) SetTitleNil(b bool)`

 SetTitleNil sets the value for Title to be an explicit nil

### UnsetTitle
`func (o *DouyinAwemeResponse) UnsetTitle()`

UnsetTitle ensures that no value is present for Title, not even an explicit nil
### GetDesc

`func (o *DouyinAwemeResponse) GetDesc() string`

GetDesc returns the Desc field if non-nil, zero value otherwise.

### GetDescOk

`func (o *DouyinAwemeResponse) GetDescOk() (*string, bool)`

GetDescOk returns a tuple with the Desc field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDesc

`func (o *DouyinAwemeResponse) SetDesc(v string)`

SetDesc sets Desc field to given value.

### HasDesc

`func (o *DouyinAwemeResponse) HasDesc() bool`

HasDesc returns a boolean if a field has been set.

### SetDescNil

`func (o *DouyinAwemeResponse) SetDescNil(b bool)`

 SetDescNil sets the value for Desc to be an explicit nil

### UnsetDesc
`func (o *DouyinAwemeResponse) UnsetDesc()`

UnsetDesc ensures that no value is present for Desc, not even an explicit nil
### GetCreateTime

`func (o *DouyinAwemeResponse) GetCreateTime() string`

GetCreateTime returns the CreateTime field if non-nil, zero value otherwise.

### GetCreateTimeOk

`func (o *DouyinAwemeResponse) GetCreateTimeOk() (*string, bool)`

GetCreateTimeOk returns a tuple with the CreateTime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreateTime

`func (o *DouyinAwemeResponse) SetCreateTime(v string)`

SetCreateTime sets CreateTime field to given value.

### HasCreateTime

`func (o *DouyinAwemeResponse) HasCreateTime() bool`

HasCreateTime returns a boolean if a field has been set.

### SetCreateTimeNil

`func (o *DouyinAwemeResponse) SetCreateTimeNil(b bool)`

 SetCreateTimeNil sets the value for CreateTime to be an explicit nil

### UnsetCreateTime
`func (o *DouyinAwemeResponse) UnsetCreateTime()`

UnsetCreateTime ensures that no value is present for CreateTime, not even an explicit nil
### GetLikedCount

`func (o *DouyinAwemeResponse) GetLikedCount() string`

GetLikedCount returns the LikedCount field if non-nil, zero value otherwise.

### GetLikedCountOk

`func (o *DouyinAwemeResponse) GetLikedCountOk() (*string, bool)`

GetLikedCountOk returns a tuple with the LikedCount field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetLikedCount

`func (o *DouyinAwemeResponse) SetLikedCount(v string)`

SetLikedCount sets LikedCount field to given value.

### HasLikedCount

`func (o *DouyinAwemeResponse) HasLikedCount() bool`

HasLikedCount returns a boolean if a field has been set.

### SetLikedCountNil

`func (o *DouyinAwemeResponse) SetLikedCountNil(b bool)`

 SetLikedCountNil sets the value for LikedCount to be an explicit nil

### UnsetLikedCount
`func (o *DouyinAwemeResponse) UnsetLikedCount()`

UnsetLikedCount ensures that no value is present for LikedCount, not even an explicit nil
### GetCommentCount

`func (o *DouyinAwemeResponse) GetCommentCount() string`

GetCommentCount returns the CommentCount field if non-nil, zero value otherwise.

### GetCommentCountOk

`func (o *DouyinAwemeResponse) GetCommentCountOk() (*string, bool)`

GetCommentCountOk returns a tuple with the CommentCount field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCommentCount

`func (o *DouyinAwemeResponse) SetCommentCount(v string)`

SetCommentCount sets CommentCount field to given value.

### HasCommentCount

`func (o *DouyinAwemeResponse) HasCommentCount() bool`

HasCommentCount returns a boolean if a field has been set.

### SetCommentCountNil

`func (o *DouyinAwemeResponse) SetCommentCountNil(b bool)`

 SetCommentCountNil sets the value for CommentCount to be an explicit nil

### UnsetCommentCount
`func (o *DouyinAwemeResponse) UnsetCommentCount()`

UnsetCommentCount ensures that no value is present for CommentCount, not even an explicit nil
### GetShareCount

`func (o *DouyinAwemeResponse) GetShareCount() string`

GetShareCount returns the ShareCount field if non-nil, zero value otherwise.

### GetShareCountOk

`func (o *DouyinAwemeResponse) GetShareCountOk() (*string, bool)`

GetShareCountOk returns a tuple with the ShareCount field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetShareCount

`func (o *DouyinAwemeResponse) SetShareCount(v string)`

SetShareCount sets ShareCount field to given value.

### HasShareCount

`func (o *DouyinAwemeResponse) HasShareCount() bool`

HasShareCount returns a boolean if a field has been set.

### SetShareCountNil

`func (o *DouyinAwemeResponse) SetShareCountNil(b bool)`

 SetShareCountNil sets the value for ShareCount to be an explicit nil

### UnsetShareCount
`func (o *DouyinAwemeResponse) UnsetShareCount()`

UnsetShareCount ensures that no value is present for ShareCount, not even an explicit nil
### GetCollectedCount

`func (o *DouyinAwemeResponse) GetCollectedCount() string`

GetCollectedCount returns the CollectedCount field if non-nil, zero value otherwise.

### GetCollectedCountOk

`func (o *DouyinAwemeResponse) GetCollectedCountOk() (*string, bool)`

GetCollectedCountOk returns a tuple with the CollectedCount field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCollectedCount

`func (o *DouyinAwemeResponse) SetCollectedCount(v string)`

SetCollectedCount sets CollectedCount field to given value.

### HasCollectedCount

`func (o *DouyinAwemeResponse) HasCollectedCount() bool`

HasCollectedCount returns a boolean if a field has been set.

### SetCollectedCountNil

`func (o *DouyinAwemeResponse) SetCollectedCountNil(b bool)`

 SetCollectedCountNil sets the value for CollectedCount to be an explicit nil

### UnsetCollectedCount
`func (o *DouyinAwemeResponse) UnsetCollectedCount()`

UnsetCollectedCount ensures that no value is present for CollectedCount, not even an explicit nil
### GetAwemeUrl

`func (o *DouyinAwemeResponse) GetAwemeUrl() string`

GetAwemeUrl returns the AwemeUrl field if non-nil, zero value otherwise.

### GetAwemeUrlOk

`func (o *DouyinAwemeResponse) GetAwemeUrlOk() (*string, bool)`

GetAwemeUrlOk returns a tuple with the AwemeUrl field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAwemeUrl

`func (o *DouyinAwemeResponse) SetAwemeUrl(v string)`

SetAwemeUrl sets AwemeUrl field to given value.

### HasAwemeUrl

`func (o *DouyinAwemeResponse) HasAwemeUrl() bool`

HasAwemeUrl returns a boolean if a field has been set.

### SetAwemeUrlNil

`func (o *DouyinAwemeResponse) SetAwemeUrlNil(b bool)`

 SetAwemeUrlNil sets the value for AwemeUrl to be an explicit nil

### UnsetAwemeUrl
`func (o *DouyinAwemeResponse) UnsetAwemeUrl()`

UnsetAwemeUrl ensures that no value is present for AwemeUrl, not even an explicit nil
### GetCoverUrl

`func (o *DouyinAwemeResponse) GetCoverUrl() string`

GetCoverUrl returns the CoverUrl field if non-nil, zero value otherwise.

### GetCoverUrlOk

`func (o *DouyinAwemeResponse) GetCoverUrlOk() (*string, bool)`

GetCoverUrlOk returns a tuple with the CoverUrl field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCoverUrl

`func (o *DouyinAwemeResponse) SetCoverUrl(v string)`

SetCoverUrl sets CoverUrl field to given value.

### HasCoverUrl

`func (o *DouyinAwemeResponse) HasCoverUrl() bool`

HasCoverUrl returns a boolean if a field has been set.

### SetCoverUrlNil

`func (o *DouyinAwemeResponse) SetCoverUrlNil(b bool)`

 SetCoverUrlNil sets the value for CoverUrl to be an explicit nil

### UnsetCoverUrl
`func (o *DouyinAwemeResponse) UnsetCoverUrl()`

UnsetCoverUrl ensures that no value is present for CoverUrl, not even an explicit nil
### GetVideoDownloadUrl

`func (o *DouyinAwemeResponse) GetVideoDownloadUrl() string`

GetVideoDownloadUrl returns the VideoDownloadUrl field if non-nil, zero value otherwise.

### GetVideoDownloadUrlOk

`func (o *DouyinAwemeResponse) GetVideoDownloadUrlOk() (*string, bool)`

GetVideoDownloadUrlOk returns a tuple with the VideoDownloadUrl field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetVideoDownloadUrl

`func (o *DouyinAwemeResponse) SetVideoDownloadUrl(v string)`

SetVideoDownloadUrl sets VideoDownloadUrl field to given value.

### HasVideoDownloadUrl

`func (o *DouyinAwemeResponse) HasVideoDownloadUrl() bool`

HasVideoDownloadUrl returns a boolean if a field has been set.

### SetVideoDownloadUrlNil

`func (o *DouyinAwemeResponse) SetVideoDownloadUrlNil(b bool)`

 SetVideoDownloadUrlNil sets the value for VideoDownloadUrl to be an explicit nil

### UnsetVideoDownloadUrl
`func (o *DouyinAwemeResponse) UnsetVideoDownloadUrl()`

UnsetVideoDownloadUrl ensures that no value is present for VideoDownloadUrl, not even an explicit nil
### GetSourceKeyword

`func (o *DouyinAwemeResponse) GetSourceKeyword() string`

GetSourceKeyword returns the SourceKeyword field if non-nil, zero value otherwise.

### GetSourceKeywordOk

`func (o *DouyinAwemeResponse) GetSourceKeywordOk() (*string, bool)`

GetSourceKeywordOk returns a tuple with the SourceKeyword field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSourceKeyword

`func (o *DouyinAwemeResponse) SetSourceKeyword(v string)`

SetSourceKeyword sets SourceKeyword field to given value.

### HasSourceKeyword

`func (o *DouyinAwemeResponse) HasSourceKeyword() bool`

HasSourceKeyword returns a boolean if a field has been set.

### SetSourceKeywordNil

`func (o *DouyinAwemeResponse) SetSourceKeywordNil(b bool)`

 SetSourceKeywordNil sets the value for SourceKeyword to be an explicit nil

### UnsetSourceKeyword
`func (o *DouyinAwemeResponse) UnsetSourceKeyword()`

UnsetSourceKeyword ensures that no value is present for SourceKeyword, not even an explicit nil

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


