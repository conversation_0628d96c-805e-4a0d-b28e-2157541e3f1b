# UserInfoResponseInput

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**StatusCode** | **int64** | 状态码 | 
**User** | Pointer to [**NullableVideoAuthor**](VideoAuthor.md) |  | [optional] 

## Methods

### NewUserInfoResponseInput

`func NewUserInfoResponseInput(statusCode int64, ) *UserInfoResponseInput`

NewUserInfoResponseInput instantiates a new UserInfoResponseInput object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewUserInfoResponseInputWithDefaults

`func NewUserInfoResponseInputWithDefaults() *UserInfoResponseInput`

NewUserInfoResponseInputWithDefaults instantiates a new UserInfoResponseInput object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetStatusCode

`func (o *UserInfoResponseInput) GetStatusCode() int64`

GetStatusCode returns the StatusCode field if non-nil, zero value otherwise.

### GetStatusCodeOk

`func (o *UserInfoResponseInput) GetStatusCodeOk() (*int64, bool)`

GetStatusCodeOk returns a tuple with the StatusCode field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetStatusCode

`func (o *UserInfoResponseInput) SetStatusCode(v int64)`

SetStatusCode sets StatusCode field to given value.


### GetUser

`func (o *UserInfoResponseInput) GetUser() VideoAuthor`

GetUser returns the User field if non-nil, zero value otherwise.

### GetUserOk

`func (o *UserInfoResponseInput) GetUserOk() (*VideoAuthor, bool)`

GetUserOk returns a tuple with the User field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUser

`func (o *UserInfoResponseInput) SetUser(v VideoAuthor)`

SetUser sets User field to given value.

### HasUser

`func (o *UserInfoResponseInput) HasUser() bool`

HasUser returns a boolean if a field has been set.

### SetUserNil

`func (o *UserInfoResponseInput) SetUserNil(b bool)`

 SetUserNil sets the value for User to be an explicit nil

### UnsetUser
`func (o *UserInfoResponseInput) UnsetUser()`

UnsetUser ensures that no value is present for User, not even an explicit nil

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


