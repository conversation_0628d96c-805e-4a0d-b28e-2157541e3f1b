# CookiesValidateResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Valid** | **bool** | Cookies是否有效 | 
**Message** | **string** | 验证结果消息 | 

## Methods

### NewCookiesValidateResponse

`func NewCookiesValidateResponse(valid bool, message string, ) *CookiesValidateResponse`

NewCookiesValidateResponse instantiates a new CookiesValidateResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewCookiesValidateResponseWithDefaults

`func NewCookiesValidateResponseWithDefaults() *CookiesValidateResponse`

NewCookiesValidateResponseWithDefaults instantiates a new CookiesValidateResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetValid

`func (o *CookiesValidateResponse) GetValid() bool`

GetValid returns the Valid field if non-nil, zero value otherwise.

### GetValidOk

`func (o *CookiesValidateResponse) GetValidOk() (*bool, bool)`

GetValidOk returns a tuple with the Valid field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetValid

`func (o *CookiesValidateResponse) SetValid(v bool)`

SetValid sets Valid field to given value.


### GetMessage

`func (o *CookiesValidateResponse) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *CookiesValidateResponse) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *CookiesValidateResponse) SetMessage(v string)`

SetMessage sets Message field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


