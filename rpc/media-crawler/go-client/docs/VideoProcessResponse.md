# VideoProcessResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**VideoId** | **string** | 视频ID | 
**InputType** | **string** | 输入类型 | 
**OriginalInput** | **string** | 原始输入 | 
**Processed** | **bool** | 是否处理成功 | 
**Data** | [**DouyinAwemeResponse**](DouyinAwemeResponse.md) | 视频数据 | 
**Source** | **string** | 数据来源 | 

## Methods

### NewVideoProcessResponse

`func NewVideoProcessResponse(videoId string, inputType string, originalInput string, processed bool, data DouyinAwemeResponse, source string, ) *VideoProcessResponse`

NewVideoProcessResponse instantiates a new VideoProcessResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewVideoProcessResponseWithDefaults

`func NewVideoProcessResponseWithDefaults() *VideoProcessResponse`

NewVideoProcessResponseWithDefaults instantiates a new VideoProcessResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetVideoId

`func (o *VideoProcessResponse) GetVideoId() string`

GetVideoId returns the VideoId field if non-nil, zero value otherwise.

### GetVideoIdOk

`func (o *VideoProcessResponse) GetVideoIdOk() (*string, bool)`

GetVideoIdOk returns a tuple with the VideoId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetVideoId

`func (o *VideoProcessResponse) SetVideoId(v string)`

SetVideoId sets VideoId field to given value.


### GetInputType

`func (o *VideoProcessResponse) GetInputType() string`

GetInputType returns the InputType field if non-nil, zero value otherwise.

### GetInputTypeOk

`func (o *VideoProcessResponse) GetInputTypeOk() (*string, bool)`

GetInputTypeOk returns a tuple with the InputType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInputType

`func (o *VideoProcessResponse) SetInputType(v string)`

SetInputType sets InputType field to given value.


### GetOriginalInput

`func (o *VideoProcessResponse) GetOriginalInput() string`

GetOriginalInput returns the OriginalInput field if non-nil, zero value otherwise.

### GetOriginalInputOk

`func (o *VideoProcessResponse) GetOriginalInputOk() (*string, bool)`

GetOriginalInputOk returns a tuple with the OriginalInput field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetOriginalInput

`func (o *VideoProcessResponse) SetOriginalInput(v string)`

SetOriginalInput sets OriginalInput field to given value.


### GetProcessed

`func (o *VideoProcessResponse) GetProcessed() bool`

GetProcessed returns the Processed field if non-nil, zero value otherwise.

### GetProcessedOk

`func (o *VideoProcessResponse) GetProcessedOk() (*bool, bool)`

GetProcessedOk returns a tuple with the Processed field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetProcessed

`func (o *VideoProcessResponse) SetProcessed(v bool)`

SetProcessed sets Processed field to given value.


### GetData

`func (o *VideoProcessResponse) GetData() DouyinAwemeResponse`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *VideoProcessResponse) GetDataOk() (*DouyinAwemeResponse, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *VideoProcessResponse) SetData(v DouyinAwemeResponse)`

SetData sets Data field to given value.


### GetSource

`func (o *VideoProcessResponse) GetSource() string`

GetSource returns the Source field if non-nil, zero value otherwise.

### GetSourceOk

`func (o *VideoProcessResponse) GetSourceOk() (*string, bool)`

GetSourceOk returns a tuple with the Source field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSource

`func (o *VideoProcessResponse) SetSource(v string)`

SetSource sets Source field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


