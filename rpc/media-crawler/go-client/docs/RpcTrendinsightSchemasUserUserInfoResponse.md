# RpcTrendinsightSchemasUserUserInfoResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Data** | Pointer to [**NullableRpcTrendinsightSchemasUserUserInfo**](RpcTrendinsightSchemasUserUserInfo.md) |  | [optional] 
**Message** | Pointer to **NullableString** |  | [optional] 

## Methods

### NewRpcTrendinsightSchemasUserUserInfoResponse

`func NewRpcTrendinsightSchemasUserUserInfoResponse() *RpcTrendinsightSchemasUserUserInfoResponse`

NewRpcTrendinsightSchemasUserUserInfoResponse instantiates a new RpcTrendinsightSchemasUserUserInfoResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewRpcTrendinsightSchemasUserUserInfoResponseWithDefaults

`func NewRpcTrendinsightSchemasUserUserInfoResponseWithDefaults() *RpcTrendinsightSchemasUserUserInfoResponse`

NewRpcTrendinsightSchemasUserUserInfoResponseWithDefaults instantiates a new RpcTrendinsightSchemasUserUserInfoResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetData

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) GetData() RpcTrendinsightSchemasUserUserInfo`

GetData returns the Data field if non-nil, zero value otherwise.

### GetDataOk

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) GetDataOk() (*RpcTrendinsightSchemasUserUserInfo, bool)`

GetDataOk returns a tuple with the Data field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetData

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) SetData(v RpcTrendinsightSchemasUserUserInfo)`

SetData sets Data field to given value.

### HasData

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) HasData() bool`

HasData returns a boolean if a field has been set.

### SetDataNil

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) SetDataNil(b bool)`

 SetDataNil sets the value for Data to be an explicit nil

### UnsetData
`func (o *RpcTrendinsightSchemasUserUserInfoResponse) UnsetData()`

UnsetData ensures that no value is present for Data, not even an explicit nil
### GetMessage

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) SetMessage(v string)`

SetMessage sets Message field to given value.

### HasMessage

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) HasMessage() bool`

HasMessage returns a boolean if a field has been set.

### SetMessageNil

`func (o *RpcTrendinsightSchemasUserUserInfoResponse) SetMessageNil(b bool)`

 SetMessageNil sets the value for Message to be an explicit nil

### UnsetMessage
`func (o *RpcTrendinsightSchemasUserUserInfoResponse) UnsetMessage()`

UnsetMessage ensures that no value is present for Message, not even an explicit nil

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


