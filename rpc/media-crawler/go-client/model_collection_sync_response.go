/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the CollectionSyncResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CollectionSyncResponse{}

// CollectionSyncResponse 收藏夹同步响应模式
type CollectionSyncResponse struct {
	// 同步的收藏夹数量
	CollectionsSynced int64 `json:"collections_synced"`
	// 同步的视频数量
	VideosSynced int64 `json:"videos_synced"`
	// 过滤后的收藏夹数量
	CollectionsFiltered int64 `json:"collections_filtered"`
	// 创建的关联关系数量
	RelationsCreated int64 `json:"relations_created"`
	// 已存在的关联关系数量
	RelationsExisting int64 `json:"relations_existing"`
	// 创建的TrendInsight视频关联数量
	TrendinsightRelationsCreated int64 `json:"trendinsight_relations_created"`
	// 已存在的TrendInsight视频关联数量
	TrendinsightRelationsExisting int64 `json:"trendinsight_relations_existing"`
	// 获取到的所有视频aweme_id列表
	AwemeIds []string `json:"aweme_ids"`
	// 视频详细信息列表
	VideoItems []VideoItemResponse `json:"video_items,omitempty"`
	// 错误信息列表
	Errors []interface{} `json:"errors,omitempty"`
}

type _CollectionSyncResponse CollectionSyncResponse

// NewCollectionSyncResponse instantiates a new CollectionSyncResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCollectionSyncResponse(collectionsSynced int64, videosSynced int64, collectionsFiltered int64, relationsCreated int64, relationsExisting int64, trendinsightRelationsCreated int64, trendinsightRelationsExisting int64, awemeIds []string) *CollectionSyncResponse {
	this := CollectionSyncResponse{}
	this.CollectionsSynced = collectionsSynced
	this.VideosSynced = videosSynced
	this.CollectionsFiltered = collectionsFiltered
	this.RelationsCreated = relationsCreated
	this.RelationsExisting = relationsExisting
	this.TrendinsightRelationsCreated = trendinsightRelationsCreated
	this.TrendinsightRelationsExisting = trendinsightRelationsExisting
	this.AwemeIds = awemeIds
	return &this
}

// NewCollectionSyncResponseWithDefaults instantiates a new CollectionSyncResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCollectionSyncResponseWithDefaults() *CollectionSyncResponse {
	this := CollectionSyncResponse{}
	return &this
}

// GetCollectionsSynced returns the CollectionsSynced field value
func (o *CollectionSyncResponse) GetCollectionsSynced() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.CollectionsSynced
}

// GetCollectionsSyncedOk returns a tuple with the CollectionsSynced field value
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetCollectionsSyncedOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CollectionsSynced, true
}

// SetCollectionsSynced sets field value
func (o *CollectionSyncResponse) SetCollectionsSynced(v int64) {
	o.CollectionsSynced = v
}

// GetVideosSynced returns the VideosSynced field value
func (o *CollectionSyncResponse) GetVideosSynced() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.VideosSynced
}

// GetVideosSyncedOk returns a tuple with the VideosSynced field value
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetVideosSyncedOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.VideosSynced, true
}

// SetVideosSynced sets field value
func (o *CollectionSyncResponse) SetVideosSynced(v int64) {
	o.VideosSynced = v
}

// GetCollectionsFiltered returns the CollectionsFiltered field value
func (o *CollectionSyncResponse) GetCollectionsFiltered() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.CollectionsFiltered
}

// GetCollectionsFilteredOk returns a tuple with the CollectionsFiltered field value
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetCollectionsFilteredOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CollectionsFiltered, true
}

// SetCollectionsFiltered sets field value
func (o *CollectionSyncResponse) SetCollectionsFiltered(v int64) {
	o.CollectionsFiltered = v
}

// GetRelationsCreated returns the RelationsCreated field value
func (o *CollectionSyncResponse) GetRelationsCreated() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.RelationsCreated
}

// GetRelationsCreatedOk returns a tuple with the RelationsCreated field value
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetRelationsCreatedOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.RelationsCreated, true
}

// SetRelationsCreated sets field value
func (o *CollectionSyncResponse) SetRelationsCreated(v int64) {
	o.RelationsCreated = v
}

// GetRelationsExisting returns the RelationsExisting field value
func (o *CollectionSyncResponse) GetRelationsExisting() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.RelationsExisting
}

// GetRelationsExistingOk returns a tuple with the RelationsExisting field value
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetRelationsExistingOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.RelationsExisting, true
}

// SetRelationsExisting sets field value
func (o *CollectionSyncResponse) SetRelationsExisting(v int64) {
	o.RelationsExisting = v
}

// GetTrendinsightRelationsCreated returns the TrendinsightRelationsCreated field value
func (o *CollectionSyncResponse) GetTrendinsightRelationsCreated() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.TrendinsightRelationsCreated
}

// GetTrendinsightRelationsCreatedOk returns a tuple with the TrendinsightRelationsCreated field value
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetTrendinsightRelationsCreatedOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.TrendinsightRelationsCreated, true
}

// SetTrendinsightRelationsCreated sets field value
func (o *CollectionSyncResponse) SetTrendinsightRelationsCreated(v int64) {
	o.TrendinsightRelationsCreated = v
}

// GetTrendinsightRelationsExisting returns the TrendinsightRelationsExisting field value
func (o *CollectionSyncResponse) GetTrendinsightRelationsExisting() int64 {
	if o == nil {
		var ret int64
		return ret
	}

	return o.TrendinsightRelationsExisting
}

// GetTrendinsightRelationsExistingOk returns a tuple with the TrendinsightRelationsExisting field value
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetTrendinsightRelationsExistingOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return &o.TrendinsightRelationsExisting, true
}

// SetTrendinsightRelationsExisting sets field value
func (o *CollectionSyncResponse) SetTrendinsightRelationsExisting(v int64) {
	o.TrendinsightRelationsExisting = v
}

// GetAwemeIds returns the AwemeIds field value
func (o *CollectionSyncResponse) GetAwemeIds() []string {
	if o == nil {
		var ret []string
		return ret
	}

	return o.AwemeIds
}

// GetAwemeIdsOk returns a tuple with the AwemeIds field value
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetAwemeIdsOk() ([]string, bool) {
	if o == nil {
		return nil, false
	}
	return o.AwemeIds, true
}

// SetAwemeIds sets field value
func (o *CollectionSyncResponse) SetAwemeIds(v []string) {
	o.AwemeIds = v
}

// GetVideoItems returns the VideoItems field value if set, zero value otherwise.
func (o *CollectionSyncResponse) GetVideoItems() []VideoItemResponse {
	if o == nil || IsNil(o.VideoItems) {
		var ret []VideoItemResponse
		return ret
	}
	return o.VideoItems
}

// GetVideoItemsOk returns a tuple with the VideoItems field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetVideoItemsOk() ([]VideoItemResponse, bool) {
	if o == nil || IsNil(o.VideoItems) {
		return nil, false
	}
	return o.VideoItems, true
}

// HasVideoItems returns a boolean if a field has been set.
func (o *CollectionSyncResponse) HasVideoItems() bool {
	if o != nil && !IsNil(o.VideoItems) {
		return true
	}

	return false
}

// SetVideoItems gets a reference to the given []VideoItemResponse and assigns it to the VideoItems field.
func (o *CollectionSyncResponse) SetVideoItems(v []VideoItemResponse) {
	o.VideoItems = v
}

// GetErrors returns the Errors field value if set, zero value otherwise.
func (o *CollectionSyncResponse) GetErrors() []interface{} {
	if o == nil || IsNil(o.Errors) {
		var ret []interface{}
		return ret
	}
	return o.Errors
}

// GetErrorsOk returns a tuple with the Errors field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CollectionSyncResponse) GetErrorsOk() ([]interface{}, bool) {
	if o == nil || IsNil(o.Errors) {
		return nil, false
	}
	return o.Errors, true
}

// HasErrors returns a boolean if a field has been set.
func (o *CollectionSyncResponse) HasErrors() bool {
	if o != nil && !IsNil(o.Errors) {
		return true
	}

	return false
}

// SetErrors gets a reference to the given []interface{} and assigns it to the Errors field.
func (o *CollectionSyncResponse) SetErrors(v []interface{}) {
	o.Errors = v
}

func (o CollectionSyncResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CollectionSyncResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["collections_synced"] = o.CollectionsSynced
	toSerialize["videos_synced"] = o.VideosSynced
	toSerialize["collections_filtered"] = o.CollectionsFiltered
	toSerialize["relations_created"] = o.RelationsCreated
	toSerialize["relations_existing"] = o.RelationsExisting
	toSerialize["trendinsight_relations_created"] = o.TrendinsightRelationsCreated
	toSerialize["trendinsight_relations_existing"] = o.TrendinsightRelationsExisting
	toSerialize["aweme_ids"] = o.AwemeIds
	if !IsNil(o.VideoItems) {
		toSerialize["video_items"] = o.VideoItems
	}
	if !IsNil(o.Errors) {
		toSerialize["errors"] = o.Errors
	}
	return toSerialize, nil
}

func (o *CollectionSyncResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"collections_synced",
		"videos_synced",
		"collections_filtered",
		"relations_created",
		"relations_existing",
		"trendinsight_relations_created",
		"trendinsight_relations_existing",
		"aweme_ids",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varCollectionSyncResponse := _CollectionSyncResponse{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varCollectionSyncResponse)

	if err != nil {
		return err
	}

	*o = CollectionSyncResponse(varCollectionSyncResponse)

	return err
}

type NullableCollectionSyncResponse struct {
	value *CollectionSyncResponse
	isSet bool
}

func (v NullableCollectionSyncResponse) Get() *CollectionSyncResponse {
	return v.value
}

func (v *NullableCollectionSyncResponse) Set(val *CollectionSyncResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableCollectionSyncResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableCollectionSyncResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCollectionSyncResponse(val *CollectionSyncResponse) *NullableCollectionSyncResponse {
	return &NullableCollectionSyncResponse{value: val, isSet: true}
}

func (v NullableCollectionSyncResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCollectionSyncResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


