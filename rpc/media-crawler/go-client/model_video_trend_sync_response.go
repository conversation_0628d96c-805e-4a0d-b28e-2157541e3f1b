/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the VideoTrendSyncResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &VideoTrendSyncResponse{}

// VideoTrendSyncResponse 视频趋势同步响应模型（用于 sync-trend 端点）
type VideoTrendSyncResponse struct {
	// 操作是否成功
	Success bool `json:"success"`
	// 操作结果消息
	Message string `json:"message"`
	Data NullableVideoTrendData `json:"data,omitempty"`
	Error NullableString `json:"error,omitempty"`
}

type _VideoTrendSyncResponse VideoTrendSyncResponse

// NewVideoTrendSyncResponse instantiates a new VideoTrendSyncResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewVideoTrendSyncResponse(success bool, message string) *VideoTrendSyncResponse {
	this := VideoTrendSyncResponse{}
	this.Success = success
	this.Message = message
	return &this
}

// NewVideoTrendSyncResponseWithDefaults instantiates a new VideoTrendSyncResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewVideoTrendSyncResponseWithDefaults() *VideoTrendSyncResponse {
	this := VideoTrendSyncResponse{}
	return &this
}

// GetSuccess returns the Success field value
func (o *VideoTrendSyncResponse) GetSuccess() bool {
	if o == nil {
		var ret bool
		return ret
	}

	return o.Success
}

// GetSuccessOk returns a tuple with the Success field value
// and a boolean to check if the value has been set.
func (o *VideoTrendSyncResponse) GetSuccessOk() (*bool, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Success, true
}

// SetSuccess sets field value
func (o *VideoTrendSyncResponse) SetSuccess(v bool) {
	o.Success = v
}

// GetMessage returns the Message field value
func (o *VideoTrendSyncResponse) GetMessage() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Message
}

// GetMessageOk returns a tuple with the Message field value
// and a boolean to check if the value has been set.
func (o *VideoTrendSyncResponse) GetMessageOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Message, true
}

// SetMessage sets field value
func (o *VideoTrendSyncResponse) SetMessage(v string) {
	o.Message = v
}

// GetData returns the Data field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *VideoTrendSyncResponse) GetData() VideoTrendData {
	if o == nil || IsNil(o.Data.Get()) {
		var ret VideoTrendData
		return ret
	}
	return *o.Data.Get()
}

// GetDataOk returns a tuple with the Data field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VideoTrendSyncResponse) GetDataOk() (*VideoTrendData, bool) {
	if o == nil {
		return nil, false
	}
	return o.Data.Get(), o.Data.IsSet()
}

// HasData returns a boolean if a field has been set.
func (o *VideoTrendSyncResponse) HasData() bool {
	if o != nil && o.Data.IsSet() {
		return true
	}

	return false
}

// SetData gets a reference to the given NullableVideoTrendData and assigns it to the Data field.
func (o *VideoTrendSyncResponse) SetData(v VideoTrendData) {
	o.Data.Set(&v)
}
// SetDataNil sets the value for Data to be an explicit nil
func (o *VideoTrendSyncResponse) SetDataNil() {
	o.Data.Set(nil)
}

// UnsetData ensures that no value is present for Data, not even an explicit nil
func (o *VideoTrendSyncResponse) UnsetData() {
	o.Data.Unset()
}

// GetError returns the Error field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *VideoTrendSyncResponse) GetError() string {
	if o == nil || IsNil(o.Error.Get()) {
		var ret string
		return ret
	}
	return *o.Error.Get()
}

// GetErrorOk returns a tuple with the Error field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *VideoTrendSyncResponse) GetErrorOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Error.Get(), o.Error.IsSet()
}

// HasError returns a boolean if a field has been set.
func (o *VideoTrendSyncResponse) HasError() bool {
	if o != nil && o.Error.IsSet() {
		return true
	}

	return false
}

// SetError gets a reference to the given NullableString and assigns it to the Error field.
func (o *VideoTrendSyncResponse) SetError(v string) {
	o.Error.Set(&v)
}
// SetErrorNil sets the value for Error to be an explicit nil
func (o *VideoTrendSyncResponse) SetErrorNil() {
	o.Error.Set(nil)
}

// UnsetError ensures that no value is present for Error, not even an explicit nil
func (o *VideoTrendSyncResponse) UnsetError() {
	o.Error.Unset()
}

func (o VideoTrendSyncResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o VideoTrendSyncResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["success"] = o.Success
	toSerialize["message"] = o.Message
	if o.Data.IsSet() {
		toSerialize["data"] = o.Data.Get()
	}
	if o.Error.IsSet() {
		toSerialize["error"] = o.Error.Get()
	}
	return toSerialize, nil
}

func (o *VideoTrendSyncResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"success",
		"message",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varVideoTrendSyncResponse := _VideoTrendSyncResponse{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varVideoTrendSyncResponse)

	if err != nil {
		return err
	}

	*o = VideoTrendSyncResponse(varVideoTrendSyncResponse)

	return err
}

type NullableVideoTrendSyncResponse struct {
	value *VideoTrendSyncResponse
	isSet bool
}

func (v NullableVideoTrendSyncResponse) Get() *VideoTrendSyncResponse {
	return v.value
}

func (v *NullableVideoTrendSyncResponse) Set(val *VideoTrendSyncResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableVideoTrendSyncResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableVideoTrendSyncResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableVideoTrendSyncResponse(val *VideoTrendSyncResponse) *NullableVideoTrendSyncResponse {
	return &NullableVideoTrendSyncResponse{value: val, isSet: true}
}

func (v NullableVideoTrendSyncResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableVideoTrendSyncResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


