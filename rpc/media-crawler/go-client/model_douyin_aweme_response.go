/*
Vue FastAPI Admin - Development

 ## Description  这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。  ### 主要功能  - 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制 - 👥 **用户管理**: 完整的用户生命周期管理 - 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统 - 📋 **菜单管理**: 动态菜单配置与权限控制 - 🔌 **API管理**: 接口权限分配与监控 - 🏢 **部门管理**: 组织架构层级管理 - 📊 **审计日志**: 完整的操作日志记录  ### 技术栈  - **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL - **认证**: JWT + Argon2 密码加密 - **文档**: 自动生成的 OpenAPI 3.0 文档 - **日志**: Loguru 结构化日志记录  ### 认证说明  大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token， 然后在请求头中添加：`Authorization: Bearer <your_token>`  ### 联系信息  - **开发者**: mizhexiaoxiao - **邮箱**: <EMAIL> - **版本**: 0.1.0         

API version: 0.1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package client

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the DouyinAwemeResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DouyinAwemeResponse{}

// DouyinAwemeResponse 抖音视频数据库模型响应（用于 db-auto-cookies 端点）
type DouyinAwemeResponse struct {
	Id NullableInt64 `json:"id,omitempty"`
	UserId NullableString `json:"user_id,omitempty"`
	SecUid NullableString `json:"sec_uid,omitempty"`
	ShortUserId NullableString `json:"short_user_id,omitempty"`
	UserUniqueId NullableString `json:"user_unique_id,omitempty"`
	Nickname NullableString `json:"nickname,omitempty"`
	Avatar NullableString `json:"avatar,omitempty"`
	UserSignature NullableString `json:"user_signature,omitempty"`
	IpLocation NullableString `json:"ip_location,omitempty"`
	// 视频ID
	AwemeId string `json:"aweme_id"`
	AwemeType NullableString `json:"aweme_type,omitempty"`
	Title NullableString `json:"title,omitempty"`
	Desc NullableString `json:"desc,omitempty"`
	CreateTime NullableString `json:"create_time,omitempty"`
	LikedCount NullableString `json:"liked_count,omitempty"`
	CommentCount NullableString `json:"comment_count,omitempty"`
	ShareCount NullableString `json:"share_count,omitempty"`
	CollectedCount NullableString `json:"collected_count,omitempty"`
	AwemeUrl NullableString `json:"aweme_url,omitempty"`
	CoverUrl NullableString `json:"cover_url,omitempty"`
	VideoDownloadUrl NullableString `json:"video_download_url,omitempty"`
	SourceKeyword NullableString `json:"source_keyword,omitempty"`
}

type _DouyinAwemeResponse DouyinAwemeResponse

// NewDouyinAwemeResponse instantiates a new DouyinAwemeResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDouyinAwemeResponse(awemeId string) *DouyinAwemeResponse {
	this := DouyinAwemeResponse{}
	this.AwemeId = awemeId
	return &this
}

// NewDouyinAwemeResponseWithDefaults instantiates a new DouyinAwemeResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDouyinAwemeResponseWithDefaults() *DouyinAwemeResponse {
	this := DouyinAwemeResponse{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetId() int64 {
	if o == nil || IsNil(o.Id.Get()) {
		var ret int64
		return ret
	}
	return *o.Id.Get()
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetIdOk() (*int64, bool) {
	if o == nil {
		return nil, false
	}
	return o.Id.Get(), o.Id.IsSet()
}

// HasId returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasId() bool {
	if o != nil && o.Id.IsSet() {
		return true
	}

	return false
}

// SetId gets a reference to the given NullableInt64 and assigns it to the Id field.
func (o *DouyinAwemeResponse) SetId(v int64) {
	o.Id.Set(&v)
}
// SetIdNil sets the value for Id to be an explicit nil
func (o *DouyinAwemeResponse) SetIdNil() {
	o.Id.Set(nil)
}

// UnsetId ensures that no value is present for Id, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetId() {
	o.Id.Unset()
}

// GetUserId returns the UserId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetUserId() string {
	if o == nil || IsNil(o.UserId.Get()) {
		var ret string
		return ret
	}
	return *o.UserId.Get()
}

// GetUserIdOk returns a tuple with the UserId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetUserIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.UserId.Get(), o.UserId.IsSet()
}

// HasUserId returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasUserId() bool {
	if o != nil && o.UserId.IsSet() {
		return true
	}

	return false
}

// SetUserId gets a reference to the given NullableString and assigns it to the UserId field.
func (o *DouyinAwemeResponse) SetUserId(v string) {
	o.UserId.Set(&v)
}
// SetUserIdNil sets the value for UserId to be an explicit nil
func (o *DouyinAwemeResponse) SetUserIdNil() {
	o.UserId.Set(nil)
}

// UnsetUserId ensures that no value is present for UserId, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetUserId() {
	o.UserId.Unset()
}

// GetSecUid returns the SecUid field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetSecUid() string {
	if o == nil || IsNil(o.SecUid.Get()) {
		var ret string
		return ret
	}
	return *o.SecUid.Get()
}

// GetSecUidOk returns a tuple with the SecUid field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetSecUidOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.SecUid.Get(), o.SecUid.IsSet()
}

// HasSecUid returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasSecUid() bool {
	if o != nil && o.SecUid.IsSet() {
		return true
	}

	return false
}

// SetSecUid gets a reference to the given NullableString and assigns it to the SecUid field.
func (o *DouyinAwemeResponse) SetSecUid(v string) {
	o.SecUid.Set(&v)
}
// SetSecUidNil sets the value for SecUid to be an explicit nil
func (o *DouyinAwemeResponse) SetSecUidNil() {
	o.SecUid.Set(nil)
}

// UnsetSecUid ensures that no value is present for SecUid, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetSecUid() {
	o.SecUid.Unset()
}

// GetShortUserId returns the ShortUserId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetShortUserId() string {
	if o == nil || IsNil(o.ShortUserId.Get()) {
		var ret string
		return ret
	}
	return *o.ShortUserId.Get()
}

// GetShortUserIdOk returns a tuple with the ShortUserId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetShortUserIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.ShortUserId.Get(), o.ShortUserId.IsSet()
}

// HasShortUserId returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasShortUserId() bool {
	if o != nil && o.ShortUserId.IsSet() {
		return true
	}

	return false
}

// SetShortUserId gets a reference to the given NullableString and assigns it to the ShortUserId field.
func (o *DouyinAwemeResponse) SetShortUserId(v string) {
	o.ShortUserId.Set(&v)
}
// SetShortUserIdNil sets the value for ShortUserId to be an explicit nil
func (o *DouyinAwemeResponse) SetShortUserIdNil() {
	o.ShortUserId.Set(nil)
}

// UnsetShortUserId ensures that no value is present for ShortUserId, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetShortUserId() {
	o.ShortUserId.Unset()
}

// GetUserUniqueId returns the UserUniqueId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetUserUniqueId() string {
	if o == nil || IsNil(o.UserUniqueId.Get()) {
		var ret string
		return ret
	}
	return *o.UserUniqueId.Get()
}

// GetUserUniqueIdOk returns a tuple with the UserUniqueId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetUserUniqueIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.UserUniqueId.Get(), o.UserUniqueId.IsSet()
}

// HasUserUniqueId returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasUserUniqueId() bool {
	if o != nil && o.UserUniqueId.IsSet() {
		return true
	}

	return false
}

// SetUserUniqueId gets a reference to the given NullableString and assigns it to the UserUniqueId field.
func (o *DouyinAwemeResponse) SetUserUniqueId(v string) {
	o.UserUniqueId.Set(&v)
}
// SetUserUniqueIdNil sets the value for UserUniqueId to be an explicit nil
func (o *DouyinAwemeResponse) SetUserUniqueIdNil() {
	o.UserUniqueId.Set(nil)
}

// UnsetUserUniqueId ensures that no value is present for UserUniqueId, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetUserUniqueId() {
	o.UserUniqueId.Unset()
}

// GetNickname returns the Nickname field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetNickname() string {
	if o == nil || IsNil(o.Nickname.Get()) {
		var ret string
		return ret
	}
	return *o.Nickname.Get()
}

// GetNicknameOk returns a tuple with the Nickname field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetNicknameOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Nickname.Get(), o.Nickname.IsSet()
}

// HasNickname returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasNickname() bool {
	if o != nil && o.Nickname.IsSet() {
		return true
	}

	return false
}

// SetNickname gets a reference to the given NullableString and assigns it to the Nickname field.
func (o *DouyinAwemeResponse) SetNickname(v string) {
	o.Nickname.Set(&v)
}
// SetNicknameNil sets the value for Nickname to be an explicit nil
func (o *DouyinAwemeResponse) SetNicknameNil() {
	o.Nickname.Set(nil)
}

// UnsetNickname ensures that no value is present for Nickname, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetNickname() {
	o.Nickname.Unset()
}

// GetAvatar returns the Avatar field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetAvatar() string {
	if o == nil || IsNil(o.Avatar.Get()) {
		var ret string
		return ret
	}
	return *o.Avatar.Get()
}

// GetAvatarOk returns a tuple with the Avatar field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetAvatarOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Avatar.Get(), o.Avatar.IsSet()
}

// HasAvatar returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasAvatar() bool {
	if o != nil && o.Avatar.IsSet() {
		return true
	}

	return false
}

// SetAvatar gets a reference to the given NullableString and assigns it to the Avatar field.
func (o *DouyinAwemeResponse) SetAvatar(v string) {
	o.Avatar.Set(&v)
}
// SetAvatarNil sets the value for Avatar to be an explicit nil
func (o *DouyinAwemeResponse) SetAvatarNil() {
	o.Avatar.Set(nil)
}

// UnsetAvatar ensures that no value is present for Avatar, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetAvatar() {
	o.Avatar.Unset()
}

// GetUserSignature returns the UserSignature field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetUserSignature() string {
	if o == nil || IsNil(o.UserSignature.Get()) {
		var ret string
		return ret
	}
	return *o.UserSignature.Get()
}

// GetUserSignatureOk returns a tuple with the UserSignature field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetUserSignatureOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.UserSignature.Get(), o.UserSignature.IsSet()
}

// HasUserSignature returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasUserSignature() bool {
	if o != nil && o.UserSignature.IsSet() {
		return true
	}

	return false
}

// SetUserSignature gets a reference to the given NullableString and assigns it to the UserSignature field.
func (o *DouyinAwemeResponse) SetUserSignature(v string) {
	o.UserSignature.Set(&v)
}
// SetUserSignatureNil sets the value for UserSignature to be an explicit nil
func (o *DouyinAwemeResponse) SetUserSignatureNil() {
	o.UserSignature.Set(nil)
}

// UnsetUserSignature ensures that no value is present for UserSignature, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetUserSignature() {
	o.UserSignature.Unset()
}

// GetIpLocation returns the IpLocation field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetIpLocation() string {
	if o == nil || IsNil(o.IpLocation.Get()) {
		var ret string
		return ret
	}
	return *o.IpLocation.Get()
}

// GetIpLocationOk returns a tuple with the IpLocation field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetIpLocationOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.IpLocation.Get(), o.IpLocation.IsSet()
}

// HasIpLocation returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasIpLocation() bool {
	if o != nil && o.IpLocation.IsSet() {
		return true
	}

	return false
}

// SetIpLocation gets a reference to the given NullableString and assigns it to the IpLocation field.
func (o *DouyinAwemeResponse) SetIpLocation(v string) {
	o.IpLocation.Set(&v)
}
// SetIpLocationNil sets the value for IpLocation to be an explicit nil
func (o *DouyinAwemeResponse) SetIpLocationNil() {
	o.IpLocation.Set(nil)
}

// UnsetIpLocation ensures that no value is present for IpLocation, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetIpLocation() {
	o.IpLocation.Unset()
}

// GetAwemeId returns the AwemeId field value
func (o *DouyinAwemeResponse) GetAwemeId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.AwemeId
}

// GetAwemeIdOk returns a tuple with the AwemeId field value
// and a boolean to check if the value has been set.
func (o *DouyinAwemeResponse) GetAwemeIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.AwemeId, true
}

// SetAwemeId sets field value
func (o *DouyinAwemeResponse) SetAwemeId(v string) {
	o.AwemeId = v
}

// GetAwemeType returns the AwemeType field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetAwemeType() string {
	if o == nil || IsNil(o.AwemeType.Get()) {
		var ret string
		return ret
	}
	return *o.AwemeType.Get()
}

// GetAwemeTypeOk returns a tuple with the AwemeType field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetAwemeTypeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.AwemeType.Get(), o.AwemeType.IsSet()
}

// HasAwemeType returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasAwemeType() bool {
	if o != nil && o.AwemeType.IsSet() {
		return true
	}

	return false
}

// SetAwemeType gets a reference to the given NullableString and assigns it to the AwemeType field.
func (o *DouyinAwemeResponse) SetAwemeType(v string) {
	o.AwemeType.Set(&v)
}
// SetAwemeTypeNil sets the value for AwemeType to be an explicit nil
func (o *DouyinAwemeResponse) SetAwemeTypeNil() {
	o.AwemeType.Set(nil)
}

// UnsetAwemeType ensures that no value is present for AwemeType, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetAwemeType() {
	o.AwemeType.Unset()
}

// GetTitle returns the Title field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetTitle() string {
	if o == nil || IsNil(o.Title.Get()) {
		var ret string
		return ret
	}
	return *o.Title.Get()
}

// GetTitleOk returns a tuple with the Title field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetTitleOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Title.Get(), o.Title.IsSet()
}

// HasTitle returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasTitle() bool {
	if o != nil && o.Title.IsSet() {
		return true
	}

	return false
}

// SetTitle gets a reference to the given NullableString and assigns it to the Title field.
func (o *DouyinAwemeResponse) SetTitle(v string) {
	o.Title.Set(&v)
}
// SetTitleNil sets the value for Title to be an explicit nil
func (o *DouyinAwemeResponse) SetTitleNil() {
	o.Title.Set(nil)
}

// UnsetTitle ensures that no value is present for Title, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetTitle() {
	o.Title.Unset()
}

// GetDesc returns the Desc field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetDesc() string {
	if o == nil || IsNil(o.Desc.Get()) {
		var ret string
		return ret
	}
	return *o.Desc.Get()
}

// GetDescOk returns a tuple with the Desc field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetDescOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.Desc.Get(), o.Desc.IsSet()
}

// HasDesc returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasDesc() bool {
	if o != nil && o.Desc.IsSet() {
		return true
	}

	return false
}

// SetDesc gets a reference to the given NullableString and assigns it to the Desc field.
func (o *DouyinAwemeResponse) SetDesc(v string) {
	o.Desc.Set(&v)
}
// SetDescNil sets the value for Desc to be an explicit nil
func (o *DouyinAwemeResponse) SetDescNil() {
	o.Desc.Set(nil)
}

// UnsetDesc ensures that no value is present for Desc, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetDesc() {
	o.Desc.Unset()
}

// GetCreateTime returns the CreateTime field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetCreateTime() string {
	if o == nil || IsNil(o.CreateTime.Get()) {
		var ret string
		return ret
	}
	return *o.CreateTime.Get()
}

// GetCreateTimeOk returns a tuple with the CreateTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetCreateTimeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.CreateTime.Get(), o.CreateTime.IsSet()
}

// HasCreateTime returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasCreateTime() bool {
	if o != nil && o.CreateTime.IsSet() {
		return true
	}

	return false
}

// SetCreateTime gets a reference to the given NullableString and assigns it to the CreateTime field.
func (o *DouyinAwemeResponse) SetCreateTime(v string) {
	o.CreateTime.Set(&v)
}
// SetCreateTimeNil sets the value for CreateTime to be an explicit nil
func (o *DouyinAwemeResponse) SetCreateTimeNil() {
	o.CreateTime.Set(nil)
}

// UnsetCreateTime ensures that no value is present for CreateTime, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetCreateTime() {
	o.CreateTime.Unset()
}

// GetLikedCount returns the LikedCount field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetLikedCount() string {
	if o == nil || IsNil(o.LikedCount.Get()) {
		var ret string
		return ret
	}
	return *o.LikedCount.Get()
}

// GetLikedCountOk returns a tuple with the LikedCount field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetLikedCountOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.LikedCount.Get(), o.LikedCount.IsSet()
}

// HasLikedCount returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasLikedCount() bool {
	if o != nil && o.LikedCount.IsSet() {
		return true
	}

	return false
}

// SetLikedCount gets a reference to the given NullableString and assigns it to the LikedCount field.
func (o *DouyinAwemeResponse) SetLikedCount(v string) {
	o.LikedCount.Set(&v)
}
// SetLikedCountNil sets the value for LikedCount to be an explicit nil
func (o *DouyinAwemeResponse) SetLikedCountNil() {
	o.LikedCount.Set(nil)
}

// UnsetLikedCount ensures that no value is present for LikedCount, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetLikedCount() {
	o.LikedCount.Unset()
}

// GetCommentCount returns the CommentCount field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetCommentCount() string {
	if o == nil || IsNil(o.CommentCount.Get()) {
		var ret string
		return ret
	}
	return *o.CommentCount.Get()
}

// GetCommentCountOk returns a tuple with the CommentCount field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetCommentCountOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.CommentCount.Get(), o.CommentCount.IsSet()
}

// HasCommentCount returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasCommentCount() bool {
	if o != nil && o.CommentCount.IsSet() {
		return true
	}

	return false
}

// SetCommentCount gets a reference to the given NullableString and assigns it to the CommentCount field.
func (o *DouyinAwemeResponse) SetCommentCount(v string) {
	o.CommentCount.Set(&v)
}
// SetCommentCountNil sets the value for CommentCount to be an explicit nil
func (o *DouyinAwemeResponse) SetCommentCountNil() {
	o.CommentCount.Set(nil)
}

// UnsetCommentCount ensures that no value is present for CommentCount, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetCommentCount() {
	o.CommentCount.Unset()
}

// GetShareCount returns the ShareCount field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetShareCount() string {
	if o == nil || IsNil(o.ShareCount.Get()) {
		var ret string
		return ret
	}
	return *o.ShareCount.Get()
}

// GetShareCountOk returns a tuple with the ShareCount field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetShareCountOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.ShareCount.Get(), o.ShareCount.IsSet()
}

// HasShareCount returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasShareCount() bool {
	if o != nil && o.ShareCount.IsSet() {
		return true
	}

	return false
}

// SetShareCount gets a reference to the given NullableString and assigns it to the ShareCount field.
func (o *DouyinAwemeResponse) SetShareCount(v string) {
	o.ShareCount.Set(&v)
}
// SetShareCountNil sets the value for ShareCount to be an explicit nil
func (o *DouyinAwemeResponse) SetShareCountNil() {
	o.ShareCount.Set(nil)
}

// UnsetShareCount ensures that no value is present for ShareCount, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetShareCount() {
	o.ShareCount.Unset()
}

// GetCollectedCount returns the CollectedCount field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetCollectedCount() string {
	if o == nil || IsNil(o.CollectedCount.Get()) {
		var ret string
		return ret
	}
	return *o.CollectedCount.Get()
}

// GetCollectedCountOk returns a tuple with the CollectedCount field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetCollectedCountOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.CollectedCount.Get(), o.CollectedCount.IsSet()
}

// HasCollectedCount returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasCollectedCount() bool {
	if o != nil && o.CollectedCount.IsSet() {
		return true
	}

	return false
}

// SetCollectedCount gets a reference to the given NullableString and assigns it to the CollectedCount field.
func (o *DouyinAwemeResponse) SetCollectedCount(v string) {
	o.CollectedCount.Set(&v)
}
// SetCollectedCountNil sets the value for CollectedCount to be an explicit nil
func (o *DouyinAwemeResponse) SetCollectedCountNil() {
	o.CollectedCount.Set(nil)
}

// UnsetCollectedCount ensures that no value is present for CollectedCount, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetCollectedCount() {
	o.CollectedCount.Unset()
}

// GetAwemeUrl returns the AwemeUrl field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetAwemeUrl() string {
	if o == nil || IsNil(o.AwemeUrl.Get()) {
		var ret string
		return ret
	}
	return *o.AwemeUrl.Get()
}

// GetAwemeUrlOk returns a tuple with the AwemeUrl field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetAwemeUrlOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.AwemeUrl.Get(), o.AwemeUrl.IsSet()
}

// HasAwemeUrl returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasAwemeUrl() bool {
	if o != nil && o.AwemeUrl.IsSet() {
		return true
	}

	return false
}

// SetAwemeUrl gets a reference to the given NullableString and assigns it to the AwemeUrl field.
func (o *DouyinAwemeResponse) SetAwemeUrl(v string) {
	o.AwemeUrl.Set(&v)
}
// SetAwemeUrlNil sets the value for AwemeUrl to be an explicit nil
func (o *DouyinAwemeResponse) SetAwemeUrlNil() {
	o.AwemeUrl.Set(nil)
}

// UnsetAwemeUrl ensures that no value is present for AwemeUrl, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetAwemeUrl() {
	o.AwemeUrl.Unset()
}

// GetCoverUrl returns the CoverUrl field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetCoverUrl() string {
	if o == nil || IsNil(o.CoverUrl.Get()) {
		var ret string
		return ret
	}
	return *o.CoverUrl.Get()
}

// GetCoverUrlOk returns a tuple with the CoverUrl field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetCoverUrlOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.CoverUrl.Get(), o.CoverUrl.IsSet()
}

// HasCoverUrl returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasCoverUrl() bool {
	if o != nil && o.CoverUrl.IsSet() {
		return true
	}

	return false
}

// SetCoverUrl gets a reference to the given NullableString and assigns it to the CoverUrl field.
func (o *DouyinAwemeResponse) SetCoverUrl(v string) {
	o.CoverUrl.Set(&v)
}
// SetCoverUrlNil sets the value for CoverUrl to be an explicit nil
func (o *DouyinAwemeResponse) SetCoverUrlNil() {
	o.CoverUrl.Set(nil)
}

// UnsetCoverUrl ensures that no value is present for CoverUrl, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetCoverUrl() {
	o.CoverUrl.Unset()
}

// GetVideoDownloadUrl returns the VideoDownloadUrl field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetVideoDownloadUrl() string {
	if o == nil || IsNil(o.VideoDownloadUrl.Get()) {
		var ret string
		return ret
	}
	return *o.VideoDownloadUrl.Get()
}

// GetVideoDownloadUrlOk returns a tuple with the VideoDownloadUrl field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetVideoDownloadUrlOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.VideoDownloadUrl.Get(), o.VideoDownloadUrl.IsSet()
}

// HasVideoDownloadUrl returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasVideoDownloadUrl() bool {
	if o != nil && o.VideoDownloadUrl.IsSet() {
		return true
	}

	return false
}

// SetVideoDownloadUrl gets a reference to the given NullableString and assigns it to the VideoDownloadUrl field.
func (o *DouyinAwemeResponse) SetVideoDownloadUrl(v string) {
	o.VideoDownloadUrl.Set(&v)
}
// SetVideoDownloadUrlNil sets the value for VideoDownloadUrl to be an explicit nil
func (o *DouyinAwemeResponse) SetVideoDownloadUrlNil() {
	o.VideoDownloadUrl.Set(nil)
}

// UnsetVideoDownloadUrl ensures that no value is present for VideoDownloadUrl, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetVideoDownloadUrl() {
	o.VideoDownloadUrl.Unset()
}

// GetSourceKeyword returns the SourceKeyword field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *DouyinAwemeResponse) GetSourceKeyword() string {
	if o == nil || IsNil(o.SourceKeyword.Get()) {
		var ret string
		return ret
	}
	return *o.SourceKeyword.Get()
}

// GetSourceKeywordOk returns a tuple with the SourceKeyword field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *DouyinAwemeResponse) GetSourceKeywordOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.SourceKeyword.Get(), o.SourceKeyword.IsSet()
}

// HasSourceKeyword returns a boolean if a field has been set.
func (o *DouyinAwemeResponse) HasSourceKeyword() bool {
	if o != nil && o.SourceKeyword.IsSet() {
		return true
	}

	return false
}

// SetSourceKeyword gets a reference to the given NullableString and assigns it to the SourceKeyword field.
func (o *DouyinAwemeResponse) SetSourceKeyword(v string) {
	o.SourceKeyword.Set(&v)
}
// SetSourceKeywordNil sets the value for SourceKeyword to be an explicit nil
func (o *DouyinAwemeResponse) SetSourceKeywordNil() {
	o.SourceKeyword.Set(nil)
}

// UnsetSourceKeyword ensures that no value is present for SourceKeyword, not even an explicit nil
func (o *DouyinAwemeResponse) UnsetSourceKeyword() {
	o.SourceKeyword.Unset()
}

func (o DouyinAwemeResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DouyinAwemeResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if o.Id.IsSet() {
		toSerialize["id"] = o.Id.Get()
	}
	if o.UserId.IsSet() {
		toSerialize["user_id"] = o.UserId.Get()
	}
	if o.SecUid.IsSet() {
		toSerialize["sec_uid"] = o.SecUid.Get()
	}
	if o.ShortUserId.IsSet() {
		toSerialize["short_user_id"] = o.ShortUserId.Get()
	}
	if o.UserUniqueId.IsSet() {
		toSerialize["user_unique_id"] = o.UserUniqueId.Get()
	}
	if o.Nickname.IsSet() {
		toSerialize["nickname"] = o.Nickname.Get()
	}
	if o.Avatar.IsSet() {
		toSerialize["avatar"] = o.Avatar.Get()
	}
	if o.UserSignature.IsSet() {
		toSerialize["user_signature"] = o.UserSignature.Get()
	}
	if o.IpLocation.IsSet() {
		toSerialize["ip_location"] = o.IpLocation.Get()
	}
	toSerialize["aweme_id"] = o.AwemeId
	if o.AwemeType.IsSet() {
		toSerialize["aweme_type"] = o.AwemeType.Get()
	}
	if o.Title.IsSet() {
		toSerialize["title"] = o.Title.Get()
	}
	if o.Desc.IsSet() {
		toSerialize["desc"] = o.Desc.Get()
	}
	if o.CreateTime.IsSet() {
		toSerialize["create_time"] = o.CreateTime.Get()
	}
	if o.LikedCount.IsSet() {
		toSerialize["liked_count"] = o.LikedCount.Get()
	}
	if o.CommentCount.IsSet() {
		toSerialize["comment_count"] = o.CommentCount.Get()
	}
	if o.ShareCount.IsSet() {
		toSerialize["share_count"] = o.ShareCount.Get()
	}
	if o.CollectedCount.IsSet() {
		toSerialize["collected_count"] = o.CollectedCount.Get()
	}
	if o.AwemeUrl.IsSet() {
		toSerialize["aweme_url"] = o.AwemeUrl.Get()
	}
	if o.CoverUrl.IsSet() {
		toSerialize["cover_url"] = o.CoverUrl.Get()
	}
	if o.VideoDownloadUrl.IsSet() {
		toSerialize["video_download_url"] = o.VideoDownloadUrl.Get()
	}
	if o.SourceKeyword.IsSet() {
		toSerialize["source_keyword"] = o.SourceKeyword.Get()
	}
	return toSerialize, nil
}

func (o *DouyinAwemeResponse) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"aweme_id",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varDouyinAwemeResponse := _DouyinAwemeResponse{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varDouyinAwemeResponse)

	if err != nil {
		return err
	}

	*o = DouyinAwemeResponse(varDouyinAwemeResponse)

	return err
}

type NullableDouyinAwemeResponse struct {
	value *DouyinAwemeResponse
	isSet bool
}

func (v NullableDouyinAwemeResponse) Get() *DouyinAwemeResponse {
	return v.value
}

func (v *NullableDouyinAwemeResponse) Set(val *DouyinAwemeResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableDouyinAwemeResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableDouyinAwemeResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDouyinAwemeResponse(val *DouyinAwemeResponse) *NullableDouyinAwemeResponse {
	return &NullableDouyinAwemeResponse{value: val, isSet: true}
}

func (v NullableDouyinAwemeResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDouyinAwemeResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


